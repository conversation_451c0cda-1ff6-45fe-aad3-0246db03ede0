/**
 * 博文交互API - /api/blog/[id]/interactions
 * 处理点赞、收藏、分享等交互功能
 */

import { NextRequest, NextResponse } from 'next/server';

import { supabase } from '@/lib/supabase';
import { generateId } from '@/lib/utils';

interface RouteParams {
  params: {
    id: string;
  };
}

// 获取用户对文章的交互状态
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id: postId } = params;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const ipAddress = getClientIP(request);

    // 获取文章的统计数据
    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .select('viewCount, likeCount, shareCount, commentCount')
      .eq('id', postId)
      .single();

    if (postError) throw postError;

    // 获取用户的交互状态
    let isLiked = false;
    let isBookmarked = false;

    if (userId) {
      // 登录用户
      const [likeResult, bookmarkResult] = await Promise.all([
        supabase
          .from('user_likes')
          .select('id')
          .eq('postId', postId)
          .eq('userId', userId)
          .single(),
        supabase
          .from('user_favorites')
          .select('id')
          .eq('postId', postId)
          .eq('userId', userId)
          .single(),
      ]);

      isLiked = !likeResult.error;
      isBookmarked = !bookmarkResult.error;
    } else {
      // 游客用户，基于IP检查点赞状态
      const { data: guestLike } = await supabase
        .from('user_likes')
        .select('id')
        .eq('postId', postId)
        .eq('ipAddress', ipAddress)
        .single();

      isLiked = !!guestLike;
    }

    return NextResponse.json({
      success: true,
      data: {
        stats: {
          views: post.viewCount || 0,
          likes: post.likeCount || 0,
          shares: post.shareCount || 0,
          comments: post.commentCount || 0,
        },
        userState: {
          isLiked,
          isBookmarked,
        },
      },
    });
  } catch (error: any) {
    console.error('Error fetching interactions:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch interactions',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// 处理交互操作
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { id: postId } = params;
    const body = await request.json();
    const { action, userId, platform } = body;
    const ipAddress = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';

    switch (action) {
      case 'like':
        return await handleLike(postId, userId, ipAddress);

      case 'unlike':
        return await handleUnlike(postId, userId, ipAddress);

      case 'bookmark':
        return await handleBookmark(postId, userId);

      case 'unbookmark':
        return await handleUnbookmark(postId, userId);

      case 'share':
        return await handleShare(
          postId,
          userId,
          platform,
          ipAddress,
          userAgent
        );

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Error processing interaction:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process interaction',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// 处理点赞
async function handleLike(postId: string, userId?: string, ipAddress?: string) {
  const { error: insertError } = await supabase.from('user_likes').insert({
    id: generateId(),
    postId,
    userId: userId || null,
    ipAddress: userId ? null : ipAddress,
  });

  if (insertError) {
    // 如果是重复点赞错误，返回已点赞状态
    if (insertError.code === '23505') {
      return NextResponse.json({
        success: true,
        data: { isLiked: true, message: 'Already liked' },
      });
    }
    throw insertError;
  }

  // 增加文章点赞数
  const { error: updateError } = await supabase.rpc('increment_like_count', {
    post_id: postId,
  });

  if (updateError) {
    console.warn('Failed to increment like count:', updateError);
  }

  return NextResponse.json({
    success: true,
    data: { isLiked: true, message: 'Liked successfully' },
  });
}

// 处理取消点赞
async function handleUnlike(
  postId: string,
  userId?: string,
  ipAddress?: string
) {
  const { error: deleteError } = await supabase
    .from('user_likes')
    .delete()
    .match(userId ? { postId, userId } : { postId, ipAddress });

  if (deleteError) throw deleteError;

  // 减少文章点赞数
  const { error: updateError } = await supabase.rpc('decrement_like_count', {
    post_id: postId,
  });

  if (updateError) {
    console.warn('Failed to decrement like count:', updateError);
  }

  return NextResponse.json({
    success: true,
    data: { isLiked: false, message: 'Unliked successfully' },
  });
}

// 处理收藏
async function handleBookmark(postId: string, userId: string) {
  if (!userId) {
    return NextResponse.json(
      { success: false, error: 'Login required for bookmarking' },
      { status: 401 }
    );
  }

  const { error } = await supabase.from('user_favorites').insert({
    id: generateId(),
    postId,
    userId,
  });

  if (error) {
    if (error.code === '23505') {
      return NextResponse.json({
        success: true,
        data: { isBookmarked: true, message: 'Already bookmarked' },
      });
    }
    throw error;
  }

  return NextResponse.json({
    success: true,
    data: { isBookmarked: true, message: 'Bookmarked successfully' },
  });
}

// 处理取消收藏
async function handleUnbookmark(postId: string, userId: string) {
  if (!userId) {
    return NextResponse.json(
      { success: false, error: 'Login required' },
      { status: 401 }
    );
  }

  const { error } = await supabase
    .from('user_favorites')
    .delete()
    .match({ postId, userId });

  if (error) throw error;

  return NextResponse.json({
    success: true,
    data: { isBookmarked: false, message: 'Unbookmarked successfully' },
  });
}

// 处理分享
async function handleShare(
  postId: string,
  userId: string | undefined,
  platform: string,
  ipAddress: string | undefined,
  userAgent: string
) {
  // 记录分享行为
  const { error: insertError } = await supabase.from('share_records').insert({
    id: generateId(),
    postId,
    userId: userId || null,
    platform,
    ipAddress,
    userAgent,
  });

  if (insertError) {
    console.warn('Failed to record share:', insertError);
  }

  // 增加分享计数
  const { error: updateError } = await supabase.rpc('increment_share_count', {
    post_id: postId,
  });

  if (updateError) {
    console.warn('Failed to increment share count:', updateError);
  }

  return NextResponse.json({
    success: true,
    data: { message: 'Share recorded successfully' },
  });
}

// 获取客户端IP地址
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0]?.trim() || '127.0.0.1';
  }

  if (realIP) {
    return realIP;
  }

  return request.ip || 'unknown';
}
