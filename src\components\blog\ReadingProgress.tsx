'use client';

import React, { useState, useEffect } from 'react';

import { cn } from '@/lib/utils';

interface ReadingProgressProps {
  target?: string; // CSS selector for the content element
  className?: string;
  showPercentage?: boolean;
  variant?: 'bar' | 'circle';
}

export function ReadingProgress({
  target = 'article',
  className,
  showPercentage = false,
  variant = 'bar',
}: ReadingProgressProps) {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const calculateProgress = () => {
      const element = document.querySelector(target);
      if (!element) return;

      const rect = element.getBoundingClientRect();
      const elementTop = rect.top + window.pageYOffset;
      const elementHeight = rect.height;
      const windowHeight = window.innerHeight;
      const scrollTop = window.pageYOffset;

      // 开始计算进度的位置（元素顶部进入视口时）
      const startProgress = elementTop - windowHeight;
      // 结束计算进度的位置（元素底部离开视口时）
      const endProgress = elementTop + elementHeight;

      if (scrollTop < startProgress) {
        setProgress(0);
        setIsVisible(false);
      } else if (scrollTop > endProgress) {
        setProgress(100);
        setIsVisible(true);
      } else {
        const currentProgress =
          ((scrollTop - startProgress) / (endProgress - startProgress)) * 100;
        setProgress(Math.min(Math.max(currentProgress, 0), 100));
        setIsVisible(true);
      }
    };

    const handleScroll = () => {
      requestAnimationFrame(calculateProgress);
    };

    // 初始计算
    calculateProgress();

    // 监听滚动事件
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', calculateProgress, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', calculateProgress);
    };
  }, [target]);

  if (variant === 'bar') {
    return (
      <div
        className={cn(
          'fixed left-0 top-0 z-50 h-1 w-full origin-left bg-mystical-500 transition-transform duration-150',
          !isVisible && 'scale-x-0',
          className
        )}
        style={{
          transform: `scaleX(${progress / 100})`,
        }}
      />
    );
  }

  if (variant === 'circle') {
    const circumference = 2 * Math.PI * 20; // radius = 20
    const strokeDashoffset = circumference - (progress / 100) * circumference;

    return (
      <div
        className={cn(
          'fixed bottom-6 right-6 z-50 h-16 w-16 transition-all duration-300',
          isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0',
          className
        )}
      >
        <div className='relative h-full w-full'>
          {/* 背景圆环 */}
          <svg
            className='h-full w-full -rotate-90 transform'
            viewBox='0 0 44 44'
          >
            <circle
              cx='22'
              cy='22'
              r='20'
              stroke='currentColor'
              strokeWidth='3'
              fill='none'
              className='text-mystical-200 dark:text-dark-600'
            />
            <circle
              cx='22'
              cy='22'
              r='20'
              stroke='currentColor'
              strokeWidth='3'
              fill='none'
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap='round'
              className='text-mystical-500 transition-all duration-300 ease-out'
            />
          </svg>

          {/* 中心内容 */}
          <div className='absolute inset-0 flex items-center justify-center'>
            <div className='flex h-12 w-12 items-center justify-center rounded-full border border-mystical-200 bg-white shadow-mystical dark:border-dark-600 dark:bg-dark-800'>
              {showPercentage ? (
                <span className='text-xs font-semibold text-mystical-600 dark:text-mystical-400'>
                  {Math.round(progress)}%
                </span>
              ) : (
                <svg
                  className='h-5 w-5 text-mystical-600 dark:text-mystical-400'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253'
                  />
                </svg>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
}

// 简化版本的阅读进度条
export function SimpleReadingProgress({ className }: { className?: string }) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const updateProgress = () => {
      const scrollTop = window.pageYOffset;
      const docHeight =
        document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      setProgress(Math.min(Math.max(scrollPercent, 0), 100));
    };

    const handleScroll = () => {
      requestAnimationFrame(updateProgress);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    updateProgress(); // 初始计算

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div
      className={cn(
        'fixed left-0 top-0 z-50 h-1 w-full origin-left bg-mystical-500 transition-transform duration-150',
        className
      )}
      style={{
        transform: `scaleX(${progress / 100})`,
      }}
    />
  );
}

// 带动画的阅读进度指示器
export function AnimatedReadingProgress({
  className,
  showOnScroll = true,
}: {
  className?: string;
  showOnScroll?: boolean;
}) {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(!showOnScroll);

  useEffect(() => {
    const updateProgress = () => {
      const scrollTop = window.pageYOffset;
      const docHeight =
        document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;

      setProgress(Math.min(Math.max(scrollPercent, 0), 100));

      if (showOnScroll) {
        setIsVisible(scrollTop > 100); // 显示在滚动100px后
      }
    };

    const handleScroll = () => {
      requestAnimationFrame(updateProgress);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    updateProgress();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [showOnScroll]);

  return (
    <div
      className={cn(
        'fixed left-0 top-0 z-50 h-1 w-full transition-all duration-300',
        isVisible ? 'opacity-100' : 'opacity-0',
        className
      )}
    >
      <div className='h-full w-full bg-mystical-200 dark:bg-dark-700'>
        <div
          className='h-full bg-gradient-to-r from-mystical-500 to-gold-500 transition-all duration-300 ease-out'
          style={{
            width: `${progress}%`,
          }}
        />
      </div>
    </div>
  );
}
