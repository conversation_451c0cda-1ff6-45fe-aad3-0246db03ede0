/**
 * 博客分类API路由 - /api/blog/categories
 * 获取博客分类列表
 */

import { NextRequest, NextResponse } from 'next/server';

import { DatabaseService } from '@/lib/database-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || undefined;

    const categories = await DatabaseService.getBlogCategories(locale);

    return NextResponse.json({
      success: true,
      data: categories,
    });
  } catch (error: any) {
    console.error('Error fetching blog categories:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch blog categories',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
