import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { Suspense } from 'react';

import { BlogList, BlogListSkeleton, BlogPagination } from '@/components/blog';
import { BlogPost, BlogCategory, Locale } from '@/types';

interface BlogCategoryPageProps {
  params: {
    locale: string;
    category: string;
  };
  searchParams: {
    page?: string;
    tag?: string;
    search?: string;
  };
}

export async function generateMetadata({
  params,
}: BlogCategoryPageProps): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const category = await getCategoryBySlug(
    params.category,
    params.locale as Locale
  );

  if (!category) {
    return {
      title: 'Category Not Found',
    };
  }

  return {
    title: category.seoTitle || `${category.name} - ${t('title')}`,
    description:
      category.seoDescription ||
      category.description ||
      t('categoryDescription', { category: category.name }),
    keywords: [category.name, category.slug],
    openGraph: {
      title: category.seoTitle || `${category.name} - ${t('title')}`,
      description:
        category.seoDescription ||
        category.description ||
        t('categoryDescription', { category: category.name }),
      type: 'website',
      locale: params.locale,
    },
  };
}

// 模拟数据获取函数
async function getCategoryBySlug(
  _categorySlug: string,
  _categoryLocale: Locale
): Promise<BlogCategory | null> {
  // 这里应该是实际的数据库查询
  // 暂时返回模拟数据
  return null;
}

async function getCategoryPosts(
  categorySlug: string,
  postsLocale: Locale,
  _currentPage: number = 1,
  _postsLimit: number = 12,
  _searchFilters?: {
    tag?: string;
    search?: string;
  }
): Promise<{
  posts: BlogPost[];
  totalPages: number;
  category: BlogCategory;
}> {
  // 这里应该是实际的数据库查询
  // 暂时返回模拟数据
  const mockCategory: BlogCategory = {
    id: '1',
    name: categorySlug,
    slug: categorySlug,
    description: `关于${categorySlug}的文章`,
    postCount: 0,
    locale: postsLocale,
    seoTitle: categorySlug,
    seoDescription: `关于${categorySlug}的文章`,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  return {
    posts: [],
    totalPages: 1,
    category: mockCategory,
  };
}

export default async function BlogCategoryPage({
  params,
  searchParams,
}: BlogCategoryPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale as Locale;

  const category = await getCategoryBySlug(params.category, locale);

  if (!category) {
    notFound();
  }

  const page = parseInt(searchParams.page || '1', 10);
  const { posts, totalPages } = await getCategoryPosts(
    params.category,
    locale,
    page,
    12,
    {
      ...(searchParams.tag && { tag: searchParams.tag }),
      ...(searchParams.search && { search: searchParams.search }),
    }
  );

  return (
    <div className='min-h-screen bg-white dark:bg-dark-900'>
      {/* 分类头部 */}
      <div className='bg-gradient-to-br from-mystical-50 to-gold-50 py-16 dark:from-dark-800 dark:to-dark-700'>
        <div className='container mx-auto px-4 text-center'>
          {/* 面包屑导航 */}
          <nav className='mb-6 flex items-center justify-center space-x-2 text-sm text-mystical-600 dark:text-mystical-400'>
            <a
              href='/blog'
              className='hover:text-mystical-700 dark:hover:text-mystical-300'
            >
              {t('blog')}
            </a>
            <span>/</span>
            <span className='font-medium text-mystical-800 dark:text-mystical-200'>
              {category.name}
            </span>
          </nav>

          <h1 className='mb-4 font-serif text-4xl font-bold text-mystical-900 dark:text-white md:text-5xl'>
            {category.name}
          </h1>

          {category.description && (
            <p className='mx-auto mb-6 max-w-2xl text-lg text-mystical-600 dark:text-mystical-300 md:text-xl'>
              {category.description}
            </p>
          )}

          <div className='flex items-center justify-center gap-4 text-sm text-mystical-500 dark:text-mystical-400'>
            <span>
              {category.postCount} {t('articles')}
            </span>
            {category.color && (
              <div
                className='h-4 w-4 rounded-full border-2 border-white dark:border-dark-800'
                style={{ backgroundColor: category.color }}
              />
            )}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className='container mx-auto px-4 py-12'>
        <div className='grid grid-cols-1 gap-8 lg:grid-cols-4'>
          {/* 主内容区 */}
          <div className='lg:col-span-3'>
            <Suspense
              fallback={<BlogListSkeleton showFeatured={false} columns={2} />}
            >
              <BlogList
                posts={posts}
                showFeatured={false}
                layout='grid'
                columns={2}
              />
            </Suspense>

            {/* 分页 */}
            {totalPages > 1 && (
              <div className='mt-12'>
                <BlogPagination
                  currentPage={page}
                  totalPages={totalPages}
                  basePath={`/blog/${params.category}`}
                />
              </div>
            )}
          </div>

          {/* 侧边栏 */}
          <div className='lg:col-span-1'>
            <CategorySidebar category={category} />
          </div>
        </div>
      </div>
    </div>
  );
}

// 分类侧边栏组件
function CategorySidebar({ category }: { category: BlogCategory }) {
  return (
    <div className='space-y-8'>
      {/* 分类信息 */}
      <div className='rounded-xl border border-mystical-200 bg-white p-6 dark:border-dark-700 dark:bg-dark-800'>
        <h3 className='mb-4 text-lg font-semibold text-mystical-900 dark:text-white'>
          关于此分类
        </h3>
        <div className='space-y-3'>
          <div className='flex items-center justify-between'>
            <span className='text-sm text-mystical-600 dark:text-mystical-400'>
              文章数量
            </span>
            <span className='text-sm font-medium text-mystical-800 dark:text-mystical-200'>
              {category.postCount}
            </span>
          </div>
          {category.color && (
            <div className='flex items-center justify-between'>
              <span className='text-sm text-mystical-600 dark:text-mystical-400'>
                分类颜色
              </span>
              <div
                className='h-6 w-6 rounded-full border border-mystical-200 dark:border-dark-600'
                style={{ backgroundColor: category.color }}
              />
            </div>
          )}
        </div>
      </div>

      {/* 相关测试推荐 */}
      <div className='rounded-xl border border-mystical-200 bg-gradient-to-br from-mystical-50 to-gold-50 p-6 dark:border-dark-600 dark:from-dark-800 dark:to-dark-700'>
        <h3 className='mb-2 text-lg font-semibold text-mystical-900 dark:text-white'>
          相关测试
        </h3>
        <p className='mb-4 text-sm text-mystical-600 dark:text-mystical-300'>
          体验与此分类相关的神秘学测试
        </p>
        <a
          href={`/${category.slug}/test`}
          className='inline-flex items-center rounded-lg bg-mystical-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-mystical-600'
        >
          开始测试 →
        </a>
      </div>

      {/* 返回博客首页 */}
      <div className='rounded-xl border border-mystical-200 bg-white p-6 dark:border-dark-700 dark:bg-dark-800'>
        <a
          href='/blog'
          className='flex items-center gap-2 text-mystical-600 transition-colors hover:text-mystical-700 dark:text-mystical-400 dark:hover:text-mystical-300'
        >
          <svg
            className='h-4 w-4'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M15 19l-7-7 7-7'
            />
          </svg>
          返回博客首页
        </a>
      </div>
    </div>
  );
}
