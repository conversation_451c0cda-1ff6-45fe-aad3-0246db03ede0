import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

// 标题组件变体
const headingVariants = cva('font-semibold tracking-tight', {
  variants: {
    level: {
      h1: 'text-4xl md:text-5xl lg:text-6xl font-bold',
      h2: 'text-3xl md:text-4xl lg:text-5xl font-bold',
      h3: 'text-2xl md:text-3xl lg:text-4xl font-semibold',
      h4: 'text-xl md:text-2xl lg:text-3xl font-semibold',
      h5: 'text-lg md:text-xl lg:text-2xl font-medium',
      h6: 'text-base md:text-lg lg:text-xl font-medium',
    },
    variant: {
      default: 'text-foreground',
      mystical: 'text-mystical-gradient',
      golden: 'text-golden-gradient',
      muted: 'text-foreground-muted',
    },
    font: {
      sans: 'font-sans',
      serif: 'font-serif',
      mystical: 'font-mystical',
      mono: 'font-mono',
    },
  },
  defaultVariants: {
    level: 'h1',
    variant: 'default',
    font: 'serif',
  },
});

// 段落组件变体
const paragraphVariants = cva('leading-relaxed', {
  variants: {
    size: {
      xs: 'text-xs',
      sm: 'text-sm',
      base: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl',
    },
    variant: {
      default: 'text-foreground',
      secondary: 'text-foreground-secondary',
      muted: 'text-foreground-muted',
      mystical: 'text-mystical-gradient',
      golden: 'text-golden-gradient',
    },
    font: {
      sans: 'font-sans',
      serif: 'font-serif',
      mystical: 'font-mystical',
      mono: 'font-mono',
    },
  },
  defaultVariants: {
    size: 'base',
    variant: 'default',
    font: 'serif',
  },
});

// 链接组件变体
const linkVariants = cva('transition-colors duration-200 underline-offset-4', {
  variants: {
    variant: {
      default:
        'text-mystical-600 hover:text-mystical-700 underline decoration-mystical-300 hover:decoration-mystical-500',
      golden:
        'text-gold-600 hover:text-gold-700 underline decoration-gold-300 hover:decoration-gold-500',
      subtle:
        'text-foreground-secondary hover:text-foreground underline decoration-border hover:decoration-foreground-muted',
      ghost:
        'text-foreground hover:text-mystical-600 no-underline hover:underline',
    },
    size: {
      sm: 'text-sm',
      base: 'text-base',
      lg: 'text-lg',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'base',
  },
});

// 标题组件接口
export interface HeadingProps
  extends React.HTMLAttributes<HTMLHeadingElement>,
    VariantProps<typeof headingVariants> {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

// 段落组件接口
export interface ParagraphProps
  extends React.HTMLAttributes<HTMLParagraphElement>,
    VariantProps<typeof paragraphVariants> {}

// 链接组件接口
export interface LinkProps
  extends React.AnchorHTMLAttributes<HTMLAnchorElement>,
    VariantProps<typeof linkVariants> {
  external?: boolean;
}

// 标题组件
const Heading = React.forwardRef<HTMLHeadingElement, HeadingProps>(
  ({ className, level = 'h1', variant, font, as, ...props }, ref) => {
    const Component = (as || level) as keyof JSX.IntrinsicElements;

    return (
      <Component
        ref={ref}
        className={cn(headingVariants({ level, variant, font, className }))}
        {...props}
      />
    );
  }
);
Heading.displayName = 'Heading';

// 段落组件
const Paragraph = React.forwardRef<HTMLParagraphElement, ParagraphProps>(
  ({ className, size, variant, font, ...props }, ref) => (
    <p
      ref={ref}
      className={cn(paragraphVariants({ size, variant, font, className }))}
      {...props}
    />
  )
);
Paragraph.displayName = 'Paragraph';

// 链接组件
const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(
  ({ className, variant, size, external = false, children, ...props }, ref) => (
    <a
      ref={ref}
      className={cn(linkVariants({ variant, size, className }))}
      {...(external && {
        target: '_blank',
        rel: 'noopener noreferrer',
      })}
      {...props}
    >
      {children}
      {external && (
        <span className='ml-1 inline-block text-xs opacity-70'>↗</span>
      )}
    </a>
  )
);
Link.displayName = 'Link';

// 文本组件 - 通用文本容器
const Text = React.forwardRef<
  HTMLElement,
  React.HTMLAttributes<HTMLElement> & {
    as?: 'span' | 'div' | 'p';
    size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl';
    weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
    variant?: 'default' | 'secondary' | 'muted' | 'mystical' | 'golden';
    font?: 'sans' | 'serif' | 'mystical' | 'mono';
  }
>(
  (
    {
      className,
      as = 'span',
      size = 'base',
      weight = 'normal',
      variant = 'default',
      font = 'sans',
      ...props
    },
    ref
  ) => {
    const Component = as as keyof JSX.IntrinsicElements;

    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      base: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl',
    };

    const weightClasses = {
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
    };

    const variantClasses = {
      default: 'text-foreground',
      secondary: 'text-foreground-secondary',
      muted: 'text-foreground-muted',
      mystical: 'text-mystical-gradient',
      golden: 'text-golden-gradient',
    };

    const fontClasses = {
      sans: 'font-sans',
      serif: 'font-serif',
      mystical: 'font-mystical',
      mono: 'font-mono',
    };

    return (
      <Component
        ref={ref}
        className={cn(
          sizeClasses[size],
          weightClasses[weight],
          variantClasses[variant],
          fontClasses[font],
          className
        )}
        {...props}
      />
    );
  }
);
Text.displayName = 'Text';

// 引用块组件
const Blockquote = React.forwardRef<
  HTMLQuoteElement,
  React.HTMLAttributes<HTMLQuoteElement> & {
    cite?: string;
    variant?: 'default' | 'mystical' | 'golden';
  }
>(({ className, cite, variant = 'default', children, ...props }, ref) => {
  const variantClasses = {
    default:
      'border-l-border bg-background-secondary text-foreground-secondary',
    mystical:
      'border-l-mystical-400 bg-mystical-50 dark:bg-mystical-900/20 text-mystical-700 dark:text-mystical-200',
    golden:
      'border-l-gold-400 bg-gold-50 dark:bg-gold-900/20 text-gold-700 dark:text-gold-200',
  };

  return (
    <blockquote
      ref={ref}
      className={cn(
        'my-6 border-l-4 py-4 pl-6 font-serif text-lg italic leading-relaxed',
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {children}
      {cite && (
        <cite className='mt-4 block font-sans text-sm not-italic opacity-70'>
          — {cite}
        </cite>
      )}
    </blockquote>
  );
});
Blockquote.displayName = 'Blockquote';

// 代码组件
const Code = React.forwardRef<
  HTMLElement,
  React.HTMLAttributes<HTMLElement> & {
    variant?: 'inline' | 'block';
  }
>(({ className, variant = 'inline', ...props }, ref) => {
  const variantClasses = {
    inline:
      'px-2 py-1 text-sm bg-background-secondary border border-border rounded font-mono',
    block:
      'block p-4 bg-dark-900 text-dark-100 border border-dark-700 rounded-lg font-mono text-sm overflow-auto',
  };

  return (
    <code
      ref={ref}
      className={cn(variantClasses[variant], className)}
      {...props}
    />
  );
});
Code.displayName = 'Code';

export {
  Heading,
  Paragraph,
  Link,
  Text,
  Blockquote,
  Code,
  headingVariants,
  paragraphVariants,
  linkVariants,
};
