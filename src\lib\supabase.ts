/**
 * Supabase 客户端配置
 * 基于database-management.md的最佳实践
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// 客户端 Supabase 实例（用于前端）
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 服务端 Supabase 实例（用于API路由，具有更高权限）
export const supabaseAdmin = supabaseServiceKey
  ? createClient(supabaseUrl, supabaseServiceKey)
  : supabase;

/**
 * 数据库健康检查
 */
export async function checkDatabaseHealth() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    return { healthy: !error, error: error?.message };
  } catch (error: any) {
    return { healthy: false, error: error.message };
  }
}

/**
 * 数据库类型定义
 * 这些类型应该与Prisma schema保持一致
 */
export interface Database {
  public: {
    Tables: {
      blog_posts: {
        Row: {
          id: string;
          title: string;
          slug: string;
          content: string;
          excerpt: string | null;
          cover_image: string | null;
          locale: string;
          category: string;
          tags: string[];
          status:
            | 'DRAFT'
            | 'PENDING'
            | 'SCHEDULED'
            | 'PUBLISHED'
            | 'ARCHIVED'
            | 'DELETED';
          published_at: string | null;
          scheduled_at: string | null;
          view_count: number;
          reading_time: number;
          featured: boolean;
          like_count: number;
          share_count: number;
          comment_count: number;
          seo_title: string | null;
          seo_description: string | null;
          keywords: string[];
          metadata: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          title: string;
          slug: string;
          content: string;
          excerpt?: string | null;
          cover_image?: string | null;
          locale: string;
          category: string;
          tags?: string[];
          status?:
            | 'DRAFT'
            | 'PENDING'
            | 'SCHEDULED'
            | 'PUBLISHED'
            | 'ARCHIVED'
            | 'DELETED';
          published_at?: string | null;
          scheduled_at?: string | null;
          view_count?: number;
          reading_time?: number;
          featured?: boolean;
          like_count?: number;
          share_count?: number;
          comment_count?: number;
          seo_title?: string | null;
          seo_description?: string | null;
          keywords?: string[];
          metadata?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          title?: string;
          slug?: string;
          content?: string;
          excerpt?: string | null;
          cover_image?: string | null;
          locale?: string;
          category?: string;
          tags?: string[];
          status?:
            | 'DRAFT'
            | 'PENDING'
            | 'SCHEDULED'
            | 'PUBLISHED'
            | 'ARCHIVED'
            | 'DELETED';
          published_at?: string | null;
          scheduled_at?: string | null;
          view_count?: number;
          reading_time?: number;
          featured?: boolean;
          like_count?: number;
          share_count?: number;
          comment_count?: number;
          seo_title?: string | null;
          seo_description?: string | null;
          keywords?: string[];
          metadata?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      blog_categories: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          color: string | null;
          icon: string | null;
          image: string | null;
          locale: string;
          post_count: number;
          seo_title: string | null;
          seo_description: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          color?: string | null;
          icon?: string | null;
          image?: string | null;
          locale: string;
          post_count?: number;
          seo_title?: string | null;
          seo_description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          color?: string | null;
          icon?: string | null;
          image?: string | null;
          locale?: string;
          post_count?: number;
          seo_title?: string | null;
          seo_description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      blog_tags: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          color: string | null;
          locale: string;
          post_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          color?: string | null;
          locale: string;
          post_count?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          color?: string | null;
          locale?: string;
          post_count?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Functions: {
      increment_view_count: {
        Args: { post_id: string };
        Returns: void;
      };
    };
  };
}
