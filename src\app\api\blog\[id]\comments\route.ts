import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { DatabaseService } from '@/lib/database-service';

// 评论创建请求验证schema
const createCommentSchema = z.object({
  content: z
    .string()
    .min(1, '评论内容不能为空')
    .max(2000, '评论内容不能超过2000字符'),
  parentId: z.string().optional(),
  userId: z.string().optional(),
  guestName: z
    .string()
    .min(1, '姓名不能为空')
    .max(100, '姓名不能超过100字符')
    .optional(),
  guestEmail: z.string().email('请输入有效的邮箱地址').optional(),
});

// 获取文章评论
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const postId = params.id;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!postId) {
      return NextResponse.json(
        { success: false, error: 'Post ID is required' },
        { status: 400 }
      );
    }

    // 获取客户端IP地址
    const clientIP =
      request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      '127.0.0.1';

    // 从数据库获取评论，包含点赞状态
    const comments = await DatabaseService.getCommentsByPostId(
      postId,
      userId || undefined,
      clientIP
    );

    return NextResponse.json({
      success: true,
      data: comments,
    });
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch comments' },
      { status: 500 }
    );
  }
}

// 创建新评论
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const postId = params.id;
    const body = await request.json();

    // 验证请求数据
    const validation = createCommentSchema.safeParse(body);
    if (!validation.success) {
      // 提供更友好的错误信息
      const firstError = validation.error.errors[0];
      let errorMessage = '请求数据无效';

      if (firstError) {
        errorMessage = firstError.message;
      }

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
          field: firstError?.path?.[0],
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { content, parentId, userId, guestName, guestEmail } =
      validation.data;

    // 验证文章是否存在
    const post = await DatabaseService.getBlogPostById(postId);
    if (!post) {
      return NextResponse.json(
        { success: false, error: 'Post not found' },
        { status: 404 }
      );
    }

    // 如果是游客评论，需要姓名和邮箱
    if (!userId && (!guestName || !guestEmail)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Guest name and email are required for guest comments',
        },
        { status: 400 }
      );
    }

    // 如果有父评论ID，验证父评论是否存在
    if (parentId) {
      const parentComment = await DatabaseService.getCommentById(parentId);
      if (!parentComment || parentComment.postId !== postId) {
        return NextResponse.json(
          { success: false, error: 'Parent comment not found' },
          { status: 404 }
        );
      }
    }

    // 创建评论
    const comment = await DatabaseService.createComment({
      content,
      postId,
      parentId: parentId || null,
      userId: userId || null,
      guestName: guestName || null,
      guestEmail: guestEmail || null,
      isApproved: true, // 直接显示，不需要审核
    });

    // 更新文章评论计数
    await DatabaseService.incrementCommentCount(postId);

    return NextResponse.json({
      success: true,
      data: comment,
    });
  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create comment' },
      { status: 500 }
    );
  }
}
