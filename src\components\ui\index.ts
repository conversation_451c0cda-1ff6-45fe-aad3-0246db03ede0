// 设计系统基础组件统一导出
// 根据01-frontend-design-rules.md设计规范实现

// 按钮组件
export { Button, buttonVariants, type ButtonProps } from './button';

// 卡片组件
export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
  cardVariants,
  type CardProps,
} from './card';

// 排版组件
export {
  Heading,
  Paragraph,
  Link,
  Text,
  Blockquote,
  Code,
  headingVariants,
  paragraphVariants,
  linkVariants,
  type HeadingProps,
  type ParagraphProps,
  type LinkProps,
} from './typography';

// 表单组件
export {
  Input,
  Textarea,
  Label,
  FormField,
  inputVariants,
  textareaVariants,
  type InputProps,
  type TextareaProps,
} from './input';

// 选择组件
export {
  Select,
  SelectGroup,
  SelectOption,
  Checkbox,
  Radio,
  selectVariants,
  type SelectProps,
} from './select';

// 布局组件
export {
  Container,
  Grid,
  Stack,
  Flex,
  Center,
  Spacer,
  Divider,
  AspectRatio,
  Section,
  containerVariants,
  gridVariants,
  stackVariants,
  type ContainerProps,
  type GridProps,
  type StackProps,
} from './layout';

// 加载组件
export {
  Spinner,
  Progress,
  Skeleton,
  LoadingDots,
  LoadingOverlay,
  SkeletonText,
  spinnerVariants,
  progressVariants,
  skeletonVariants,
  type SpinnerProps,
  type ProgressProps,
  type SkeletonProps,
} from './loading';

// 徽章和标签组件
export {
  Badge,
  Tag,
  StatusBadge,
  NotificationBadge,
  TagGroup,
  badgeVariants,
  tagVariants,
  type BadgeProps,
  type TagProps,
} from './badge';

// 主题切换组件
export { ThemeToggle } from './theme-toggle';

// Toast通知组件
export { showToast, type ToastProps } from './toast';

// 组件变体类型
export type { VariantProps } from 'class-variance-authority';

// 工具函数
export { cn } from '@/lib/utils';

// 设计系统常量
export const DESIGN_TOKENS = {
  // 颜色系统
  colors: {
    mystical: {
      50: '#faf7ff',
      100: '#f3ecff',
      200: '#e9d8ff',
      300: '#d8b9ff',
      400: '#c084fc',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7c3aed',
      800: '#6b21a8',
      900: '#581c87',
    },
    gold: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
    },
    zodiac: {
      fire: '#ff6b6b',
      earth: '#51cf66',
      air: '#74c0fc',
      water: '#845ef7',
    },
  },

  // 字体系统
  fonts: {
    sans: ['Inter', 'Noto Sans', 'system-ui', 'sans-serif'],
    serif: ['Playfair Display', 'Noto Serif', 'Georgia', 'serif'],
    mystical: ['Cinzel', 'Trajan Pro', 'serif'],
    mono: ['JetBrains Mono', 'Fira Code', 'Consolas', 'monospace'],
  },

  // 间距系统
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
  },

  // 断点系统
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // 阴影系统
  shadows: {
    mystical:
      '0 10px 25px -3px rgba(168, 85, 247, 0.1), 0 4px 6px -2px rgba(168, 85, 247, 0.05)',
    'mystical-lg':
      '0 20px 40px -4px rgba(168, 85, 247, 0.15), 0 8px 16px -4px rgba(168, 85, 247, 0.1)',
    gold: '0 10px 25px -3px rgba(245, 158, 11, 0.1), 0 4px 6px -2px rgba(245, 158, 11, 0.05)',
  },

  // 动画系统
  animations: {
    'mystical-glow': 'mysticalGlow 3s ease-in-out infinite alternate',
    'tarot-flip': 'tarotFlip 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
    'crystal-shine': 'crystalShine 2s ease-in-out infinite',
    'star-twinkle': 'starTwinkle 1.5s ease-in-out infinite alternate',
    'fade-in': 'fadeIn 0.6s ease-out',
    'slide-up': 'slideUp 0.5s ease-out',
    'scale-in': 'scaleIn 0.3s ease-out',
    float: 'float 3s ease-in-out infinite',
  },
} as const;

// 组件使用指南
export const COMPONENT_USAGE = {
  Button: {
    description: '按钮组件，支持多种变体和状态',
    variants: ['default', 'mystical', 'golden', 'outline', 'ghost', 'link'],
    sizes: ['xs', 'sm', 'default', 'lg', 'xl'],
    examples: [
      '<Button variant="mystical">神秘按钮</Button>',
      '<Button variant="golden" size="lg">黄金按钮</Button>',
      '<Button loading>加载中...</Button>',
    ],
  },
  Card: {
    description: '卡片组件，用于内容容器',
    variants: ['default', 'mystical', 'golden', 'glass', 'elevated', 'outline'],
    sizes: ['xs', 'sm', 'default', 'lg', 'xl'],
    examples: [
      '<Card variant="mystical">神秘卡片</Card>',
      '<Card interactive="hover">悬停效果</Card>',
      '<Card glow>发光效果</Card>',
    ],
  },
  Typography: {
    description: '排版组件，支持多种字体和样式',
    components: ['Heading', 'Paragraph', 'Link', 'Text', 'Blockquote', 'Code'],
    fonts: ['sans', 'serif', 'mystical', 'mono'],
    examples: [
      '<Heading level="h1" font="mystical">神秘标题</Heading>',
      '<Paragraph variant="mystical">神秘段落</Paragraph>',
      '<Link variant="golden">黄金链接</Link>',
    ],
  },
} as const;
