'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { cn } from '@/lib/utils';
import { BlogPost } from '@/types';

import { BlogCard } from './BlogCard';

interface BlogListProps {
  posts: BlogPost[];
  featuredPost?: BlogPost;
  className?: string;
  showFeatured?: boolean;
  layout?: 'grid' | 'list' | 'medium';
  columns?: 1 | 2 | 3;
  totalPosts?: number;
  currentPage?: number;
  totalPages?: number;
}

export function BlogList({
  posts,
  featuredPost,
  className,
  showFeatured = true,
  layout = 'medium',
  columns = 2,
  totalPosts: _totalPosts,
  currentPage = 1,
  totalPages = 1,
}: BlogListProps) {
  const t = useTranslations('blog');

  // Medium风格的布局类
  const containerClasses = cn(
    {
      'space-y-12': layout === 'medium',
      'space-y-8': layout !== 'medium',
    },
    className
  );

  const gridClasses = cn('gap-6', {
    // Medium风格：单列布局
    'space-y-8': layout === 'medium',
    // 传统网格布局
    grid: layout === 'grid' || layout === 'list',
    'grid-cols-1': layout === 'list' || columns === 1,
    'grid-cols-1 md:grid-cols-2': layout === 'grid' && columns === 2,
    'grid-cols-1 md:grid-cols-2 lg:grid-cols-3':
      layout === 'grid' && columns === 3,
  });

  // 分离特色文章和常规文章
  const regularPosts = featuredPost
    ? posts.filter(post => post.id !== featuredPost.id)
    : posts;

  return (
    <div className={containerClasses}>
      {/* Featured Post Section - Medium风格 */}
      {showFeatured && featuredPost && (
        <section className='mb-12'>
          <BlogCard
            post={featuredPost}
            variant='featured'
            className='mb-12 border-b border-mystical-200 pb-12 dark:border-dark-700'
          />
        </section>
      )}

      {/* Regular Posts Section - Medium风格 */}
      {regularPosts.length > 0 && (
        <section>
          <div className={gridClasses}>
            {regularPosts.map((post, index) => (
              <article
                key={post.id}
                className={cn(
                  'border-b border-mystical-200 pb-8 dark:border-dark-700',
                  {
                    'mb-8':
                      layout === 'medium' && index < regularPosts.length - 1,
                    'border-b-0 pb-0':
                      layout === 'medium' && index === regularPosts.length - 1,
                  }
                )}
              >
                {layout === 'medium' ? (
                  <MediumStyleCard post={post} />
                ) : (
                  <BlogCard
                    post={post}
                    variant={layout === 'list' ? 'compact' : 'default'}
                    className={layout === 'list' ? 'flex gap-4' : ''}
                  />
                )}
              </article>
            ))}
          </div>

          {/* 分页导航 - Medium风格 */}
          {layout === 'medium' && totalPages > 1 && (
            <nav className='mt-12 flex justify-center border-t border-mystical-200 pt-8 dark:border-dark-700'>
              <div className='flex items-center gap-2'>
                {currentPage > 1 && (
                  <a
                    href={`?page=${currentPage - 1}`}
                    className='px-4 py-2 text-sm font-medium text-mystical-600 transition-colors hover:text-mystical-700 dark:text-mystical-400 dark:hover:text-mystical-300'
                  >
                    ← Previous
                  </a>
                )}

                <span className='px-4 py-2 text-sm text-mystical-500 dark:text-mystical-400'>
                  Page {currentPage} of {totalPages}
                </span>

                {currentPage < totalPages && (
                  <a
                    href={`?page=${currentPage + 1}`}
                    className='px-4 py-2 text-sm font-medium text-mystical-600 transition-colors hover:text-mystical-700 dark:text-mystical-400 dark:hover:text-mystical-300'
                  >
                    Next →
                  </a>
                )}
              </div>
            </nav>
          )}
        </section>
      )}

      {/* Empty State */}
      {posts.length === 0 && (
        <div className='py-12 text-center'>
          <div className='mx-auto max-w-md'>
            <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-mystical-100 dark:bg-dark-700'>
              <svg
                className='h-8 w-8 text-mystical-400'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                />
              </svg>
            </div>
            <h3 className='mb-2 text-lg font-semibold text-mystical-900 dark:text-white'>
              {t('noPosts')}
            </h3>
            <p className='text-mystical-600 dark:text-mystical-300'>
              {t('noPostsDescription')}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

// Medium风格的文章卡片组件
function MediumStyleCard({ post }: { post: BlogPost }) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(post.locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className='grid grid-cols-1 items-start gap-6 md:grid-cols-3'>
      {/* 内容区域 */}
      <div className='order-2 md:order-1 md:col-span-2'>
        {/* 分类标签 */}
        <div className='mb-2'>
          <span className='text-xs font-semibold uppercase tracking-wide text-mystical-600 dark:text-mystical-400'>
            {post.category}
          </span>
        </div>

        {/* 标题 */}
        <h2 className='mb-3 font-serif text-xl font-bold leading-tight text-mystical-900 dark:text-white md:text-2xl'>
          <a
            href={`/blog/${post.category}/${post.slug}`}
            className='transition-colors hover:text-mystical-700 dark:hover:text-mystical-300'
          >
            {post.title}
          </a>
        </h2>

        {/* 摘要 */}
        {post.excerpt && (
          <p className='mb-4 line-clamp-3 leading-relaxed text-mystical-600 dark:text-mystical-300'>
            {post.excerpt}
          </p>
        )}

        {/* 元信息 */}
        <div className='flex items-center gap-4 text-sm text-mystical-500 dark:text-mystical-400'>
          <div className='flex items-center gap-1'>
            <svg
              className='h-4 w-4'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
              />
            </svg>
            <span>{formatDate(post.publishedAt || post.createdAt)}</span>
          </div>
          <div className='flex items-center gap-1'>
            <svg
              className='h-4 w-4'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
              />
            </svg>
            <span>{post.readingTime} min read</span>
          </div>
          <div className='flex items-center gap-1'>
            <svg
              className='h-4 w-4'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M15 12a3 3 0 11-6 0 3 3 0 016 0z'
              />
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z'
              />
            </svg>
            <span>{post.viewCount}</span>
          </div>
        </div>
      </div>

      {/* 图片区域 */}
      {post.coverImage && (
        <div className='order-1 md:order-2 md:col-span-1'>
          <a href={`/blog/${post.category}/${post.slug}`}>
            <div className='relative aspect-[4/3] overflow-hidden rounded-lg'>
              <img
                src={post.coverImage}
                alt={post.title}
                className='h-full w-full object-cover transition-transform duration-300 hover:scale-105'
              />
            </div>
          </a>
        </div>
      )}
    </div>
  );
}

// 博客列表骨架屏组件
export function BlogListSkeleton({
  showFeatured = true,
  columns = 2,
}: {
  showFeatured?: boolean;
  columns?: 1 | 2 | 3;
}) {
  const gridClasses = cn('grid gap-6', {
    'grid-cols-1': columns === 1,
    'grid-cols-1 md:grid-cols-2': columns === 2,
    'grid-cols-1 md:grid-cols-2 lg:grid-cols-3': columns === 3,
  });

  return (
    <div className='animate-pulse space-y-8'>
      {/* Featured Post Skeleton */}
      {showFeatured && (
        <section className='mb-12'>
          <div className='mb-6'>
            <div className='mb-2 h-8 w-48 rounded bg-mystical-200 dark:bg-dark-700'></div>
            <div className='h-4 w-96 rounded bg-mystical-100 dark:bg-dark-600'></div>
          </div>
          <div className='mx-auto max-w-4xl rounded-xl border border-mystical-200 bg-mystical-50 p-8 dark:border-dark-700 dark:bg-dark-800'>
            <div className='mb-8 aspect-[16/9] rounded-xl bg-mystical-200 dark:bg-dark-700'></div>
            <div className='mb-4 h-4 w-24 rounded bg-mystical-200 dark:bg-dark-700'></div>
            <div className='mb-4 h-8 w-3/4 rounded bg-mystical-200 dark:bg-dark-700'></div>
            <div className='mb-6 space-y-2'>
              <div className='h-4 rounded bg-mystical-100 dark:bg-dark-600'></div>
              <div className='h-4 w-5/6 rounded bg-mystical-100 dark:bg-dark-600'></div>
              <div className='h-4 w-4/6 rounded bg-mystical-100 dark:bg-dark-600'></div>
            </div>
            <div className='flex items-center gap-3'>
              <div className='h-8 w-8 rounded-full bg-mystical-200 dark:bg-dark-700'></div>
              <div className='h-4 w-32 rounded bg-mystical-200 dark:bg-dark-700'></div>
            </div>
          </div>
        </section>
      )}

      {/* Regular Posts Skeleton */}
      <section>
        <div className='mb-6'>
          <div className='mb-2 h-8 w-40 rounded bg-mystical-200 dark:bg-dark-700'></div>
          <div className='h-4 w-80 rounded bg-mystical-100 dark:bg-dark-600'></div>
        </div>

        <div className={gridClasses}>
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className='rounded-xl border border-mystical-200 bg-white p-6 dark:border-dark-700 dark:bg-dark-800'
            >
              <div className='mb-6 aspect-[16/9] rounded-lg bg-mystical-200 dark:bg-dark-700'></div>
              <div className='mb-3 h-4 w-20 rounded bg-mystical-200 dark:bg-dark-700'></div>
              <div className='mb-3 h-6 w-4/5 rounded bg-mystical-200 dark:bg-dark-700'></div>
              <div className='mb-4 space-y-2'>
                <div className='h-3 rounded bg-mystical-100 dark:bg-dark-600'></div>
                <div className='h-3 w-5/6 rounded bg-mystical-100 dark:bg-dark-600'></div>
              </div>
              <div className='mb-4 flex items-center gap-3'>
                <div className='h-6 w-6 rounded-full bg-mystical-200 dark:bg-dark-700'></div>
                <div className='h-3 w-24 rounded bg-mystical-200 dark:bg-dark-700'></div>
              </div>
              <div className='flex justify-between'>
                <div className='flex gap-4'>
                  <div className='h-3 w-16 rounded bg-mystical-100 dark:bg-dark-600'></div>
                  <div className='h-3 w-12 rounded bg-mystical-100 dark:bg-dark-600'></div>
                </div>
                <div className='flex gap-3'>
                  <div className='h-3 w-8 rounded bg-mystical-100 dark:bg-dark-600'></div>
                  <div className='h-3 w-8 rounded bg-mystical-100 dark:bg-dark-600'></div>
                  <div className='h-3 w-8 rounded bg-mystical-100 dark:bg-dark-600'></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
