-- 数据库函数定义
-- Database Functions for Blog Interactions

-- 增加浏览次数
CREATE OR REPLACE FUNCTION increment_view_count(post_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE blog_posts 
  SET "viewCount" = "viewCount" + 1 
  WHERE id = post_id;
END;
$$ LANGUAGE plpgsql;

-- 增加点赞次数
CREATE OR REPLACE FUNCTION increment_like_count(post_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE blog_posts 
  SET "likeCount" = "likeCount" + 1 
  WHERE id = post_id;
END;
$$ LANGUAGE plpgsql;

-- 减少点赞次数
CREATE OR REPLACE FUNCTION decrement_like_count(post_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE blog_posts 
  SET "likeCount" = GREATEST("likeCount" - 1, 0)
  WHERE id = post_id;
END;
$$ LANGUAGE plpgsql;

-- 增加分享次数
CREATE OR REPLACE FUNCTION increment_share_count(post_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE blog_posts 
  SET "shareCount" = "shareCount" + 1 
  WHERE id = post_id;
END;
$$ LANGUAGE plpgsql;

-- 增加评论次数
CREATE OR REPLACE FUNCTION increment_comment_count(post_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE blog_posts 
  SET "commentCount" = "commentCount" + 1 
  WHERE id = post_id;
END;
$$ LANGUAGE plpgsql;

-- 减少评论次数
CREATE OR REPLACE FUNCTION decrement_comment_count(post_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE blog_posts 
  SET "commentCount" = GREATEST("commentCount" - 1, 0)
  WHERE id = post_id;
END;
$$ LANGUAGE plpgsql;

-- 获取用户交互状态
CREATE OR REPLACE FUNCTION get_user_interaction_status(
  post_id TEXT,
  user_id TEXT DEFAULT NULL,
  ip_address TEXT DEFAULT NULL
)
RETURNS TABLE(is_liked BOOLEAN, is_bookmarked BOOLEAN) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(
      (SELECT TRUE FROM user_likes 
       WHERE "postId" = post_id 
       AND (
         (user_id IS NOT NULL AND "userId" = user_id) OR
         (user_id IS NULL AND "ipAddress" = ip_address)
       )
       LIMIT 1), 
      FALSE
    ) as is_liked,
    COALESCE(
      (SELECT TRUE FROM user_favorites 
       WHERE "postId" = post_id 
       AND "userId" = user_id
       LIMIT 1), 
      FALSE
    ) as is_bookmarked;
END;
$$ LANGUAGE plpgsql;

-- 批量更新文章统计数据
CREATE OR REPLACE FUNCTION update_post_stats(post_id TEXT)
RETURNS VOID AS $$
BEGIN
  UPDATE blog_posts 
  SET 
    "viewCount" = (
      SELECT COUNT(*) FROM blog_views 
      WHERE "postId" = post_id
    ),
    "likeCount" = (
      SELECT COUNT(*) FROM user_likes 
      WHERE "postId" = post_id
    ),
    "shareCount" = (
      SELECT COUNT(*) FROM share_records 
      WHERE "postId" = post_id
    ),
    "commentCount" = (
      SELECT COUNT(*) FROM comments 
      WHERE "postId" = post_id AND "isApproved" = TRUE
    )
  WHERE id = post_id;
END;
$$ LANGUAGE plpgsql;
