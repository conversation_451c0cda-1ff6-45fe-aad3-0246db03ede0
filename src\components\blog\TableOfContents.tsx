'use client';

import { List, ChevronRight } from 'lucide-react';
import React, { useState, useEffect, useMemo } from 'react';

import { cn } from '@/lib/utils';

interface TocItem {
  id: string;
  title: string;
  level: number;
}

interface TableOfContentsProps {
  content: string;
  className?: string;
  variant?: 'sidebar' | 'floating' | 'inline';
}

/**
 * 从HTML内容中提取标题
 */
function extractHeadings(htmlContent: string): TocItem[] {
  const headings: TocItem[] = [];
  const headingRegex =
    /<h([1-6])([^>]*?)(?:\s+id=['"]([^'"]*?)['"])?[^>]*?>(.*?)<\/h[1-6]>/gi;

  let match;
  while ((match = headingRegex.exec(htmlContent)) !== null) {
    if (!match[1] || !match[4]) continue; // 跳过无效匹配

    const level = parseInt(match[1], 10);
    let id = match[3]; // 从id属性获取
    const title = match[4].replace(/<[^>]*>/g, '').trim(); // 移除HTML标签

    if (!title) continue; // 跳过空标题

    // 如果没有id，生成一个
    if (!id) {
      id = title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();
    }

    // 只包含H2-H4级别的标题，避免过于复杂
    if (level >= 2 && level <= 4) {
      headings.push({ id, title, level });
    }
  }

  return headings;
}

/**
 * 从Markdown内容中提取标题
 */
function extractMarkdownHeadings(markdownContent: string): TocItem[] {
  const headings: TocItem[] = [];
  const lines = markdownContent.split('\n');

  for (const line of lines) {
    const match = line.match(/^(#{1,6})\s+(.+)$/);
    if (match && match[1] && match[2]) {
      const level = match[1].length;
      const title = match[2].trim();

      // 只包含H2-H4级别的标题
      if (level >= 2 && level <= 4) {
        const id = title
          .toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .trim();

        headings.push({ id, title, level });
      }
    }
  }

  return headings;
}

export function TableOfContents({
  content,
  className,
  variant = 'sidebar',
}: TableOfContentsProps) {
  const [activeId, setActiveId] = useState<string>('');
  const [isVisible, setIsVisible] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // 提取标题
  const headings = useMemo(() => {
    // 检查内容格式
    if (content.includes('<h') && content.includes('</h')) {
      // HTML格式
      return extractHeadings(content);
    } else {
      // Markdown格式
      return extractMarkdownHeadings(content);
    }
  }, [content]);

  // 监听滚动，高亮当前标题
  useEffect(() => {
    if (headings.length === 0) return;

    const handleScroll = () => {
      const scrollTop = window.pageYOffset;

      // 浮动模式的可见性控制
      if (variant === 'floating') {
        setIsVisible(scrollTop > 200);
      }

      // 查找当前激活的标题
      let currentActiveId = '';

      for (let i = headings.length - 1; i >= 0; i--) {
        const heading = headings[i];
        if (!heading) continue;

        const element = document.getElementById(heading.id);

        if (element) {
          const rect = element.getBoundingClientRect();
          // 如果标题在视窗上方100px以内，认为是当前激活的
          if (rect.top <= 100) {
            currentActiveId = heading.id;
            break;
          }
        }
      }

      setActiveId(currentActiveId);
    };

    handleScroll(); // 初始调用
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [headings, variant]);

  // 点击标题跳转
  const handleItemClick = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const yOffset = -80; // 偏移量，避免被固定头部遮挡
      const y =
        element.getBoundingClientRect().top + window.pageYOffset + yOffset;

      window.scrollTo({
        top: y,
        behavior: 'smooth',
      });
    }
  };

  // 如果没有标题，不显示目录
  if (headings.length === 0) {
    return null;
  }

  // 浮动模式
  if (variant === 'floating') {
    return (
      <div
        className={cn(
          'fixed right-6 top-1/2 z-40 -translate-y-1/2 transition-all duration-300',
          isVisible
            ? 'translate-x-0 opacity-100'
            : 'pointer-events-none translate-x-4 opacity-0',
          className
        )}
      >
        <div className='max-w-xs rounded-lg border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-800'>
          <div className='p-4'>
            <div className='mb-3 flex items-center justify-between'>
              <h3 className='flex items-center gap-2 text-sm font-semibold text-gray-900 dark:text-gray-100'>
                <List className='h-4 w-4' />
                目录
              </h3>
              <button
                onClick={() => setIsCollapsed(!isCollapsed)}
                className='text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              >
                <ChevronRight
                  className={cn(
                    'h-4 w-4 transition-transform',
                    isCollapsed ? 'rotate-0' : 'rotate-90'
                  )}
                />
              </button>
            </div>

            {!isCollapsed && (
              <nav className='space-y-1'>
                {headings.map(heading => (
                  <button
                    key={heading.id}
                    onClick={() => handleItemClick(heading.id)}
                    className={cn(
                      'block w-full rounded px-2 py-1 text-left text-sm transition-colors duration-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                      heading.level === 2 && 'font-medium',
                      heading.level === 3 &&
                        'pl-4 text-gray-600 dark:text-gray-400',
                      heading.level === 4 &&
                        'pl-6 text-gray-500 dark:text-gray-500',
                      activeId === heading.id
                        ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                        : 'text-gray-700 dark:text-gray-300'
                    )}
                  >
                    {heading.title}
                  </button>
                ))}
              </nav>
            )}
          </div>
        </div>
      </div>
    );
  }

  // 内联模式
  if (variant === 'inline') {
    return (
      <div
        className={cn(
          'my-8 rounded-lg bg-gray-50 p-6 dark:bg-gray-800/50',
          className
        )}
      >
        <h3 className='mb-4 flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-gray-100'>
          <List className='h-5 w-5' />
          目录
        </h3>
        <nav className='space-y-2'>
          {headings.map(heading => (
            <button
              key={heading.id}
              onClick={() => handleItemClick(heading.id)}
              className={cn(
                'block w-full rounded px-3 py-2 text-left transition-colors duration-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                heading.level === 2 && 'text-base font-medium',
                heading.level === 3 &&
                  'pl-6 text-sm text-gray-600 dark:text-gray-400',
                heading.level === 4 &&
                  'pl-9 text-sm text-gray-500 dark:text-gray-500',
                activeId === heading.id
                  ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                  : 'text-gray-700 dark:text-gray-300'
              )}
            >
              {heading.title}
            </button>
          ))}
        </nav>
      </div>
    );
  }

  // 默认侧边栏模式
  return (
    <div className={cn('sticky top-24', className)}>
      <div className='rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800/50'>
        <h3 className='mb-3 flex items-center gap-2 text-sm font-semibold text-gray-900 dark:text-gray-100'>
          <List className='h-4 w-4' />
          目录
        </h3>
        <nav className='space-y-1'>
          {headings.map(heading => (
            <button
              key={heading.id}
              onClick={() => handleItemClick(heading.id)}
              className={cn(
                'block w-full rounded px-2 py-1.5 text-left text-sm transition-colors duration-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                heading.level === 2 && 'font-medium',
                heading.level === 3 && 'pl-4 text-gray-600 dark:text-gray-400',
                heading.level === 4 && 'pl-6 text-gray-500 dark:text-gray-500',
                activeId === heading.id
                  ? 'border-l-2 border-blue-600 bg-blue-50 text-blue-600 dark:border-blue-400 dark:bg-blue-900/20 dark:text-blue-400'
                  : 'text-gray-700 dark:text-gray-300'
              )}
            >
              {heading.title}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
}
