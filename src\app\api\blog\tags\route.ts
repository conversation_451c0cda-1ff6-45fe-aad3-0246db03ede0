/**
 * 博客标签API路由 - /api/blog/tags
 * 获取博客标签列表
 */

import { NextRequest, NextResponse } from 'next/server';

import { DatabaseService } from '@/lib/database-service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const locale = searchParams.get('locale') || undefined;

    const tags = await DatabaseService.getBlogTags(locale);

    return NextResponse.json({
      success: true,
      data: tags,
    });
  } catch (error: any) {
    console.error('Error fetching blog tags:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch blog tags',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
