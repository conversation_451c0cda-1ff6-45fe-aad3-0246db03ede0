'use client';

import { <PERSON>u, X, ChevronDown } from 'lucide-react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { LanguageSwitcher } from '@/components/ui/language-switcher';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { cn } from '@/lib/utils';

interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
}

export function Header() {
  const t = useTranslations('navigation');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const navigation: NavigationItem[] = [
    {
      label: t('home'),
      href: '/',
    },
    {
      label: t('tarot'),
      href: '/tarot',
      children: [
        { label: 'Tarot Reading', href: '/tarot/test' },
        { label: 'Tarot Cards', href: '/tarot/cards' },
        { label: 'Tarot Guide', href: '/tarot/guide' },
        { label: 'Tarot History', href: '/tarot/history' },
      ],
    },
    {
      label: t('astrology'),
      href: '/astrology',
      children: [
        { label: 'Astrology Test', href: '/astrology/test' },
        { label: 'Zodiac Signs', href: '/astrology/signs' },
        { label: 'Compatibility', href: '/astrology/compatibility' },
        { label: 'Horoscope', href: '/astrology/horoscope' },
      ],
    },
    {
      label: t('numerology'),
      href: '/numerology',
      children: [
        { label: 'Numerology Test', href: '/numerology/test' },
        { label: 'Number Calculator', href: '/numerology/calculator' },
        { label: 'Number Meanings', href: '/numerology/meanings' },
      ],
    },
    {
      label: t('blog'),
      href: '/blog',
    },
  ];

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    setActiveDropdown(null);
  };

  const toggleDropdown = (label: string) => {
    setActiveDropdown(activeDropdown === label ? null : label);
  };

  return (
    <header className='sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60'>
      <div className='container mx-auto px-4'>
        <div className='flex h-16 items-center'>
          {/* Logo - 固定宽度确保居中对齐 */}
          <div className='w-48 flex-shrink-0'>
            <Link href='/' className='flex items-center space-x-2'>
              <div className='flex h-8 w-8 items-center justify-center rounded-full bg-mystical-gradient'>
                <span className='text-sm font-bold text-white'>M</span>
              </div>
              <span className='bg-mystical-gradient bg-clip-text font-mystical text-xl font-bold text-transparent'>
                Mystical
              </span>
            </Link>
          </div>

          {/* Desktop Navigation - 居中显示 */}
          <nav className='hidden flex-1 items-center justify-center space-x-8 md:flex'>
            {navigation.map(item => (
              <div key={item.label} className='group relative'>
                {item.children ? (
                  <>
                    <button
                      className='flex items-center space-x-1 text-foreground/80 transition-colors hover:text-foreground'
                      onMouseEnter={() => setActiveDropdown(item.label)}
                    >
                      <span>{item.label}</span>
                      <ChevronDown className='h-4 w-4' />
                    </button>

                    {/* Dropdown Menu */}
                    <div
                      className={cn(
                        'invisible absolute left-0 top-full mt-2 w-48 rounded-lg border border-border bg-card opacity-0 shadow-lg transition-all duration-200 group-hover:visible group-hover:opacity-100',
                        'before:absolute before:-top-2 before:left-0 before:h-2 before:w-full'
                      )}
                      onMouseLeave={() => setActiveDropdown(null)}
                    >
                      <div className='py-2'>
                        {item.children.map(child => (
                          <Link
                            key={child.href}
                            href={child.href}
                            className='hover:bg-accent/50 block px-4 py-2 text-sm text-foreground/80 transition-colors hover:text-foreground'
                          >
                            {child.label}
                          </Link>
                        ))}
                      </div>
                    </div>
                  </>
                ) : (
                  <Link
                    href={item.href}
                    className='text-foreground/80 transition-colors hover:text-foreground'
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Language Switcher, Theme Toggle and CTA Button - 固定宽度确保居中对齐 */}
          <div className='hidden w-48 flex-shrink-0 items-center justify-end space-x-4 md:flex'>
            <LanguageSwitcher variant='compact' />
            <ThemeToggle />
            <Button variant='mystical' size='sm'>
              Start Free Test
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className='p-2 md:hidden'
            onClick={toggleMenu}
            aria-label='Toggle menu'
          >
            {isMenuOpen ? (
              <X className='h-6 w-6' />
            ) : (
              <Menu className='h-6 w-6' />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className='border-t border-border/40 py-4 md:hidden'>
            <nav className='space-y-2'>
              {navigation.map(item => (
                <div key={item.label}>
                  {item.children ? (
                    <>
                      <button
                        className='flex w-full items-center justify-between px-4 py-2 text-left text-foreground/80 transition-colors hover:text-foreground'
                        onClick={() => toggleDropdown(item.label)}
                      >
                        <span>{item.label}</span>
                        <ChevronDown
                          className={cn(
                            'h-4 w-4 transition-transform',
                            activeDropdown === item.label && 'rotate-180'
                          )}
                        />
                      </button>

                      {activeDropdown === item.label && (
                        <div className='space-y-1 pl-4'>
                          {item.children.map(child => (
                            <Link
                              key={child.href}
                              href={child.href}
                              className='block px-4 py-2 text-sm text-foreground/60 transition-colors hover:text-foreground'
                              onClick={() => setIsMenuOpen(false)}
                            >
                              {child.label}
                            </Link>
                          ))}
                        </div>
                      )}
                    </>
                  ) : (
                    <Link
                      href={item.href}
                      className='block px-4 py-2 text-foreground/80 transition-colors hover:text-foreground'
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.label}
                    </Link>
                  )}
                </div>
              ))}

              <div className='space-y-3 px-4 pt-4'>
                <LanguageSwitcher variant='mobile' />
                <div className='flex justify-center'>
                  <ThemeToggle />
                </div>
                <Button variant='mystical' size='sm' className='w-full'>
                  Start Free Test
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
