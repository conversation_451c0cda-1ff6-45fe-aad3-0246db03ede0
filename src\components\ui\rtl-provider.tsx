'use client';

import { useLocale } from 'next-intl';
import { createContext, useContext, useEffect, useState } from 'react';

import { rtlLocales, type Locale } from '@/i18n';

interface RTLContextType {
  isRTL: boolean;
  direction: 'ltr' | 'rtl';
  locale: Locale;
  textAlign: 'left' | 'right';
  marginStart: 'ml' | 'mr';
  marginEnd: 'mr' | 'ml';
  paddingStart: 'pl' | 'pr';
  paddingEnd: 'pr' | 'pl';
  borderStart: 'border-l' | 'border-r';
  borderEnd: 'border-r' | 'border-l';
  roundedStart: 'rounded-l' | 'rounded-r';
  roundedEnd: 'rounded-r' | 'rounded-l';
}

const RTLContext = createContext<RTLContextType | undefined>(undefined);

interface RTLProviderProps {
  children: React.ReactNode;
  locale?: Locale;
}

export function RTLProvider({
  children,
  locale: propLocale,
}: RTLProviderProps) {
  const currentLocale = useLocale() as Locale;
  const locale = propLocale || currentLocale;
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const isRTL = rtlLocales.includes(locale);
  const direction = isRTL ? 'rtl' : 'ltr';

  const contextValue: RTLContextType = {
    isRTL,
    direction,
    locale,
    textAlign: isRTL ? 'right' : 'left',
    marginStart: isRTL ? 'mr' : 'ml',
    marginEnd: isRTL ? 'ml' : 'mr',
    paddingStart: isRTL ? 'pr' : 'pl',
    paddingEnd: isRTL ? 'pl' : 'pr',
    borderStart: isRTL ? 'border-r' : 'border-l',
    borderEnd: isRTL ? 'border-l' : 'border-r',
    roundedStart: isRTL ? 'rounded-r' : 'rounded-l',
    roundedEnd: isRTL ? 'rounded-l' : 'rounded-r',
  };

  // 在服务端渲染时返回默认值
  if (!isClient) {
    return (
      <RTLContext.Provider value={contextValue}>
        <div dir={direction}>{children}</div>
      </RTLContext.Provider>
    );
  }

  return (
    <RTLContext.Provider value={contextValue}>
      <div dir={direction} className={isRTL ? 'rtl' : 'ltr'}>
        {children}
      </div>
    </RTLContext.Provider>
  );
}

export function useRTL() {
  const context = useContext(RTLContext);
  if (context === undefined) {
    throw new Error('useRTL must be used within an RTLProvider');
  }
  return context;
}

/**
 * RTL-aware utility functions
 * RTL感知的工具函数
 */
export const rtlUtils = {
  /**
   * 获取方向感知的类名
   */
  getDirectionalClass(
    ltrClass: string,
    rtlClass: string,
    isRTL?: boolean
  ): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    return rtl ? rtlClass : ltrClass;
  },

  /**
   * 获取开始边距类名
   */
  getMarginStart(size: string, isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    return rtl ? `mr-${size}` : `ml-${size}`;
  },

  /**
   * 获取结束边距类名
   */
  getMarginEnd(size: string, isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    return rtl ? `ml-${size}` : `mr-${size}`;
  },

  /**
   * 获取开始内边距类名
   */
  getPaddingStart(size: string, isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    return rtl ? `pr-${size}` : `pl-${size}`;
  },

  /**
   * 获取结束内边距类名
   */
  getPaddingEnd(size: string, isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    return rtl ? `pl-${size}` : `pr-${size}`;
  },

  /**
   * 获取文本对齐类名
   */
  getTextAlign(isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    return rtl ? 'text-right' : 'text-left';
  },

  /**
   * 获取Flex方向类名
   */
  getFlexDirection(isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    return rtl ? 'flex-row-reverse' : 'flex-row';
  },

  /**
   * 获取浮动类名
   */
  getFloat(side: 'start' | 'end', isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    if (side === 'start') {
      return rtl ? 'float-right' : 'float-left';
    } else {
      return rtl ? 'float-left' : 'float-right';
    }
  },

  /**
   * 获取边框类名
   */
  getBorder(side: 'start' | 'end', isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    if (side === 'start') {
      return rtl ? 'border-r' : 'border-l';
    } else {
      return rtl ? 'border-l' : 'border-r';
    }
  },

  /**
   * 获取圆角类名
   */
  getRounded(
    side: 'start' | 'end',
    size: string = '',
    isRTL?: boolean
  ): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    const sizeClass = size ? `-${size}` : '';

    if (side === 'start') {
      return rtl ? `rounded-r${sizeClass}` : `rounded-l${sizeClass}`;
    } else {
      return rtl ? `rounded-l${sizeClass}` : `rounded-r${sizeClass}`;
    }
  },

  /**
   * 转换transform值以适应RTL
   */
  transformForRTL(transform: string, isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    if (!rtl) return transform;

    // 翻转translateX值
    return transform.replace(/translateX\(([^)]+)\)/g, (_, value) => {
      if (value.includes('-')) {
        return `translateX(${value.replace('-', '')})`;
      } else {
        return `translateX(-${value})`;
      }
    });
  },

  /**
   * 获取图标旋转类名（用于箭头等）
   */
  getIconRotation(isRTL?: boolean): string {
    const rtl = isRTL ?? rtlLocales.includes(useLocale());
    return rtl ? 'rotate-180' : '';
  },
};

/**
 * RTL-aware component wrapper
 * RTL感知的组件包装器
 */
interface RTLAwareProps {
  children: React.ReactNode;
  className?: string;
  as?: keyof JSX.IntrinsicElements;
}

export function RTLAware({
  children,
  className = '',
  as: Component = 'div',
}: RTLAwareProps) {
  const { isRTL, direction } = useRTL();

  return (
    <Component
      dir={direction}
      className={`${className} ${isRTL ? 'rtl' : 'ltr'}`}
    >
      {children}
    </Component>
  );
}

/**
 * Hook for RTL-aware styling
 * RTL感知样式的Hook
 */
export function useRTLStyles() {
  const { isRTL } = useRTL();

  return {
    isRTL,
    marginStart: (size: string) => (isRTL ? `mr-${size}` : `ml-${size}`),
    marginEnd: (size: string) => (isRTL ? `ml-${size}` : `mr-${size}`),
    paddingStart: (size: string) => (isRTL ? `pr-${size}` : `pl-${size}`),
    paddingEnd: (size: string) => (isRTL ? `pl-${size}` : `pr-${size}`),
    textAlign: isRTL ? 'text-right' : 'text-left',
    float: (side: 'start' | 'end') =>
      side === 'start'
        ? isRTL
          ? 'float-right'
          : 'float-left'
        : isRTL
          ? 'float-left'
          : 'float-right',
    border: (side: 'start' | 'end') =>
      side === 'start'
        ? isRTL
          ? 'border-r'
          : 'border-l'
        : isRTL
          ? 'border-l'
          : 'border-r',
    rounded: (side: 'start' | 'end', size: string = '') => {
      const sizeClass = size ? `-${size}` : '';
      return side === 'start'
        ? isRTL
          ? `rounded-r${sizeClass}`
          : `rounded-l${sizeClass}`
        : isRTL
          ? `rounded-l${sizeClass}`
          : `rounded-r${sizeClass}`;
    },
  };
}

/**
 * RTL-aware CSS-in-JS styles
 * RTL感知的CSS-in-JS样式
 */
export function useRTLCSSStyles() {
  const { isRTL } = useRTL();

  return {
    isRTL,
    direction: isRTL ? 'rtl' : 'ltr',
    textAlign: isRTL ? 'right' : 'left',
    marginLeft: (value: string | number) => ({
      [isRTL ? 'marginRight' : 'marginLeft']: value,
    }),
    marginRight: (value: string | number) => ({
      [isRTL ? 'marginLeft' : 'marginRight']: value,
    }),
    paddingLeft: (value: string | number) => ({
      [isRTL ? 'paddingRight' : 'paddingLeft']: value,
    }),
    paddingRight: (value: string | number) => ({
      [isRTL ? 'paddingLeft' : 'paddingRight']: value,
    }),
    borderLeft: (value: string) => ({
      [isRTL ? 'borderRight' : 'borderLeft']: value,
    }),
    borderRight: (value: string) => ({
      [isRTL ? 'borderLeft' : 'borderRight']: value,
    }),
    left: (value: string | number) => ({ [isRTL ? 'right' : 'left']: value }),
    right: (value: string | number) => ({ [isRTL ? 'left' : 'right']: value }),
    transform: (value: string) => ({
      transform: isRTL ? rtlUtils.transformForRTL(value, true) : value,
    }),
  };
}
