'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from 'next-intl';
import { useEffect } from 'react';

import { locales, defaultLocale, type Locale } from '@/i18n';

/**
 * Hook for handling language persistence and automatic redirection
 * 处理语言持久化和自动重定向的Hook
 */
export function useLanguagePersistence() {
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = useLocale() as Locale;

  useEffect(() => {
    // 只在客户端执行
    if (typeof window === 'undefined') return;

    // 获取存储的语言偏好
    const storedLocale = localStorage.getItem('preferred-locale') as Locale;

    // 如果没有存储的偏好，尝试从浏览器语言检测
    if (!storedLocale) {
      const browserLocale = detectBrowserLanguage();
      if (browserLocale && browserLocale !== currentLocale) {
        // 保存检测到的语言并重定向
        localStorage.setItem('preferred-locale', browserLocale);
        redirectToLocale(browserLocale);
        return;
      }

      // 如果没有检测到合适的语言，保存当前语言
      localStorage.setItem('preferred-locale', currentLocale);
      return;
    }

    // 如果存储的语言与当前语言不同，重定向
    if (storedLocale !== currentLocale && locales.includes(storedLocale)) {
      redirectToLocale(storedLocale);
    }
  }, [currentLocale, pathname, router]);

  /**
   * 检测浏览器语言偏好
   */
  const detectBrowserLanguage = (): Locale | null => {
    if (typeof navigator === 'undefined') return null;

    // 获取浏览器语言列表
    const browserLanguages = navigator.languages || [navigator.language];

    for (const browserLang of browserLanguages) {
      // 精确匹配
      const exactMatch = locales.find(locale => {
        const config = require('@/i18n').languageConfig[locale];
        return config.locale === browserLang || config.hreflang === browserLang;
      });

      if (exactMatch) return exactMatch;

      // 语言代码匹配（忽略地区）
      const langCode = browserLang.split('-')[0];
      const langMatch = locales.find(locale => locale === langCode);

      if (langMatch) return langMatch;

      // 特殊情况处理
      if (langCode === 'zh') {
        // 中文优先匹配简体中文
        return 'zh-CN';
      }
    }

    return null;
  };

  /**
   * 重定向到指定语言
   */
  const redirectToLocale = (locale: Locale) => {
    const segments = pathname.split('/');
    segments[1] = locale;
    const newPath = segments.join('/');
    router.replace(newPath);
  };

  /**
   * 手动设置语言偏好
   */
  const setLanguagePreference = (locale: Locale) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-locale', locale);
    }
  };

  /**
   * 获取存储的语言偏好
   */
  const getStoredLanguagePreference = (): Locale | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('preferred-locale') as Locale;
  };

  /**
   * 清除语言偏好
   */
  const clearLanguagePreference = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('preferred-locale');
    }
  };

  return {
    currentLocale,
    setLanguagePreference,
    getStoredLanguagePreference,
    clearLanguagePreference,
    detectBrowserLanguage,
  };
}

/**
 * 语言检测工具函数
 */
export const languageUtils = {
  /**
   * 检测用户的首选语言
   */
  detectUserPreferredLanguage(): Locale {
    // 1. 检查localStorage中的偏好
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('preferred-locale') as Locale;
      if (stored && locales.includes(stored)) {
        return stored;
      }
    }

    // 2. 检查浏览器语言
    if (typeof navigator !== 'undefined') {
      const browserLanguages = navigator.languages || [navigator.language];

      for (const browserLang of browserLanguages) {
        // 精确匹配
        for (const locale of locales) {
          const config = require('@/i18n').languageConfig[locale];
          if (
            config.locale === browserLang ||
            config.hreflang === browserLang
          ) {
            return locale;
          }
        }

        // 语言代码匹配
        const langCode = browserLang.split('-')[0];
        if (locales.includes(langCode as Locale)) {
          return langCode as Locale;
        }
      }
    }

    // 3. 返回默认语言
    return defaultLocale;
  },

  /**
   * 获取语言的显示名称
   */
  getLanguageDisplayName(locale: Locale, useNativeName = true): string {
    const config = require('@/i18n').languageConfig[locale];
    return useNativeName ? config.nativeName : config.name;
  },

  /**
   * 检查是否为RTL语言
   */
  isRTLLanguage(locale: string): boolean {
    const rtlLocales = require('@/i18n').rtlLocales;
    return rtlLocales.includes(locale);
  },

  /**
   * 获取语言的文字方向
   */
  getTextDirection(locale: Locale): 'ltr' | 'rtl' {
    const config = require('@/i18n').languageConfig[locale];
    return config.direction;
  },

  /**
   * 获取语言的字体系列
   */
  getFontFamily(locale: Locale): string {
    const config = require('@/i18n').languageConfig[locale];
    return config.fontFamily;
  },

  /**
   * 获取语言的文字扩展因子
   */
  getExpansionFactor(locale: Locale): number {
    const config = require('@/i18n').languageConfig[locale];
    return config.expansionFactor;
  },

  /**
   * 格式化URL路径为多语言路径
   */
  formatLocalizedPath(path: string, locale: Locale): string {
    // 移除开头的斜杠
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;

    // 如果路径已经包含语言代码，替换它
    const segments = cleanPath.split('/');
    if (locales.includes(segments[0] as Locale)) {
      segments[0] = locale;
    } else {
      // 如果没有语言代码，添加它
      segments.unshift(locale);
    }

    return '/' + segments.join('/');
  },

  /**
   * 从路径中提取语言代码
   */
  extractLocaleFromPath(path: string): Locale | null {
    const segments = path.split('/');
    const potentialLocale = segments[1];

    if (potentialLocale && locales.includes(potentialLocale as Locale)) {
      return potentialLocale as Locale;
    }

    return null;
  },
};
