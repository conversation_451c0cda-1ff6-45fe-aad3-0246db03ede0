import React from 'react';
import { remark } from 'remark';
import remarkGfm from 'remark-gfm';
import remarkHtml from 'remark-html';

import { cn } from '@/lib/utils';

interface MarkdownContentProps {
  content: string;
  className?: string;
}

/**
 * 移除内容中的第一个H1标题（避免与页面标题重复）
 */
function removeFirstH1(htmlContent: string): string {
  // 匹配第一个H1标签并移除
  return htmlContent.replace(/<h1[^>]*>.*?<\/h1>/i, '');
}

/**
 * 为HTML标题添加ID属性，用于锚点导航
 */
function addHeadingIds(htmlContent: string): string {
  return htmlContent.replace(
    /<h([1-6])([^>]*)>([^<]+)<\/h[1-6]>/gi,
    (match, level, attributes, title) => {
      // 检查是否已经有ID
      if (attributes.includes('id=')) {
        return match;
      }

      // 生成ID
      const id = title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim();

      return `<h${level}${attributes} id="${id}">${title}</h${level}>`;
    }
  );
}

/**
 * 服务端Markdown处理函数
 * 将Markdown转换为HTML，确保SEO友好
 */
function processMarkdownContent(content: string): string {
  try {
    let processedHtml = '';

    // 检查内容是否已经是HTML格式
    if (content.includes('<') && content.includes('>')) {
      // 如果已经是HTML，直接使用
      processedHtml = content;
    } else {
      // 如果是Markdown，转换为HTML
      const result = remark()
        .use(remarkGfm) // 支持GitHub Flavored Markdown
        .use(remarkHtml, { sanitize: false }) // 允许HTML标签
        .processSync(content);
      processedHtml = String(result);
    }

    // 移除第一个H1标题（避免与页面标题重复）
    processedHtml = removeFirstH1(processedHtml);

    // 添加标题ID用于锚点导航
    return addHeadingIds(processedHtml);
  } catch (error) {
    console.error('Error processing markdown:', error);
    // 如果处理失败，返回原始内容
    return content;
  }
}

/**
 * Markdown内容渲染组件 - SSR优化版本
 * 基于01-frontend-design-rules.md的博客文章设计规范
 */
export function MarkdownContent({ content, className }: MarkdownContentProps) {
  // 在服务端处理Markdown内容
  const processedContent = processMarkdownContent(content);

  return (
    <article
      id='article'
      className={cn(
        // Medium风格的基础样式
        'prose prose-lg max-w-none',

        // 标题样式 - 优化阅读体验的颜色方案
        // H1: mobile: 2rem(32px), desktop: 2.5rem(40px)
        '[&>h1]:font-serif [&>h1]:font-bold [&>h1]:text-gray-900 [&>h1]:dark:text-gray-50',
        '[&>h1]:mb-6 [&>h1]:mt-12 [&>h1]:text-4xl [&>h1]:leading-tight [&>h1]:tracking-tight [&>h1]:md:text-5xl',

        // H2: mobile: 1.75rem(28px), desktop: 2rem(32px)
        '[&>h2]:font-serif [&>h2]:font-bold [&>h2]:text-gray-900 [&>h2]:dark:text-gray-50',
        '[&>h2]:mb-4 [&>h2]:mt-10 [&>h2]:text-3xl [&>h2]:leading-tight [&>h2]:tracking-tight [&>h2]:md:text-4xl',

        // H3: mobile: 1.5rem(24px), desktop: 1.75rem(28px)
        '[&>h3]:font-serif [&>h3]:font-semibold [&>h3]:text-gray-800 [&>h3]:dark:text-gray-100',
        '[&>h3]:mb-4 [&>h3]:mt-8 [&>h3]:text-2xl [&>h3]:leading-snug [&>h3]:md:text-3xl',

        // H4: mobile: 1.25rem(20px), desktop: 1.5rem(24px)
        '[&>h4]:font-serif [&>h4]:font-semibold [&>h4]:text-gray-800 [&>h4]:dark:text-gray-100',
        '[&>h4]:mb-3 [&>h4]:mt-6 [&>h4]:text-xl [&>h4]:leading-snug [&>h4]:md:text-2xl',

        '[&>h5]:font-serif [&>h5]:font-semibold [&>h5]:text-gray-700 [&>h5]:dark:text-gray-200',
        '[&>h5]:mb-2 [&>h5]:mt-4 [&>h5]:text-lg',

        '[&>h6]:font-serif [&>h6]:font-semibold [&>h6]:text-gray-700 [&>h6]:dark:text-gray-200',
        '[&>h6]:mb-2 [&>h6]:mt-4 [&>h6]:text-base',

        // 段落样式 - 优化的正文阅读颜色
        // mobile: 1.125rem(18px), desktop: 1.25rem(20px), 行高: 1.75
        '[&>p]:mb-6 [&>p]:leading-7 [&>p]:text-gray-700 [&>p]:dark:text-gray-300',
        '[&>p]:font-serif [&>p]:text-lg [&>p]:tracking-tight [&>p]:md:text-xl',

        // 首段特殊样式 - mobile: 1.25rem(20px), desktop: 1.375rem(22px)
        '[&>p:first-of-type]:text-xl [&>p:first-of-type]:font-normal [&>p:first-of-type]:text-gray-800 [&>p:first-of-type]:dark:text-gray-200 [&>p:first-of-type]:md:text-2xl',

        // 列表样式 - 优化的阅读颜色
        '[&>ul]:mb-6 [&>ul]:pl-6',
        '[&>ul>li]:mb-2 [&>ul>li]:text-lg [&>ul>li]:leading-7 [&>ul>li]:text-gray-700 [&>ul>li]:dark:text-gray-300 [&>ul>li]:md:text-xl',
        '[&>ul>li]:list-disc [&>ul>li]:marker:text-gray-500 [&>ul>li]:dark:marker:text-gray-400',
        // 列表中的强调文本
        '[&>ul>li_strong]:font-bold [&>ul>li_strong]:text-gray-900 [&>ul>li_strong]:dark:text-gray-50',
        '[&>ul>li_em]:italic [&>ul>li_em]:text-gray-800 [&>ul>li_em]:dark:text-gray-100',

        '[&>ol]:mb-6 [&>ol]:pl-6',
        '[&>ol>li]:mb-2 [&>ol>li]:text-lg [&>ol>li]:leading-7 [&>ol>li]:text-gray-700 [&>ol>li]:dark:text-gray-300 [&>ol>li]:md:text-xl',
        '[&>ol>li]:list-decimal [&>ol>li]:marker:font-semibold [&>ol>li]:marker:text-gray-500 [&>ol>li]:dark:marker:text-gray-400',
        // 有序列表中的强调文本
        '[&>ol>li_strong]:font-bold [&>ol>li_strong]:text-gray-900 [&>ol>li_strong]:dark:text-gray-50',
        '[&>ol>li_em]:italic [&>ol>li_em]:text-gray-800 [&>ol>li_em]:dark:text-gray-100',

        // 引用块样式 - 优化的引用颜色
        // mobile: 1.25rem(20px), desktop: 1.375rem(22px), 行高: 1.6
        '[&>blockquote]:font-serif [&>blockquote]:text-xl [&>blockquote]:italic [&>blockquote]:md:text-2xl',
        '[&>blockquote]:text-gray-600 [&>blockquote]:dark:text-gray-300',
        '[&>blockquote]:my-8 [&>blockquote]:px-8 [&>blockquote]:py-6',
        '[&>blockquote]:bg-gray-50 [&>blockquote]:dark:bg-gray-800/50',
        '[&>blockquote]:border-l-4 [&>blockquote]:border-gray-300 [&>blockquote]:dark:border-gray-600',
        '[&>blockquote]:rounded-r-lg [&>blockquote]:leading-relaxed',
        // 引用块中的强调文本
        '[&>blockquote_strong]:font-bold [&>blockquote_strong]:text-gray-800 [&>blockquote_strong]:dark:text-gray-100',
        '[&>blockquote_em]:italic [&>blockquote_em]:text-gray-700 [&>blockquote_em]:dark:text-gray-200',

        // 代码样式 - 优化的代码块颜色
        '[&>pre]:rounded-lg [&>pre]:bg-gray-900 [&>pre]:p-6 [&>pre]:text-gray-100 [&>pre]:dark:bg-gray-950',
        '[&>pre]:my-8 [&>pre]:overflow-auto [&>pre]:border [&>pre]:border-gray-700 [&>pre]:dark:border-gray-800',
        '[&>pre]:font-mono [&>pre]:text-sm [&>pre]:leading-relaxed',

        '[&>p>code]:bg-gray-100 [&>p>code]:dark:bg-gray-800',
        '[&>p>code]:text-gray-800 [&>p>code]:dark:text-gray-200',
        '[&>p>code]:rounded [&>p>code]:px-2 [&>p>code]:py-1 [&>p>code]:text-sm',
        '[&>p>code]:border [&>p>code]:border-gray-200 [&>p>code]:font-mono [&>p>code]:dark:border-gray-700',

        // 图片样式 - 优化的图片边框
        '[&>p>img]:my-8 [&>p>img]:h-auto [&>p>img]:w-full [&>p>img]:rounded-lg',
        '[&>p>img]:border [&>p>img]:border-gray-200 [&>p>img]:shadow-lg [&>p>img]:dark:border-gray-700',

        // 链接样式 - 优化的链接颜色，保持品牌特色
        '[&>p>a]:text-blue-600 [&>p>a]:dark:text-blue-400',
        '[&>p>a]:underline [&>p>a]:decoration-blue-300 [&>p>a]:underline-offset-2 [&>p>a]:dark:decoration-blue-500',
        '[&>p>a]:decoration-1 [&>p>a]:transition-colors',
        '[&>p>a:hover]:text-blue-700 [&>p>a:hover]:dark:text-blue-300',
        '[&>p>a:hover]:decoration-blue-500 [&>p>a:hover]:dark:decoration-blue-400',

        // 分隔线样式 - 优化的分隔线颜色
        '[&>hr]:my-12 [&>hr]:h-px [&>hr]:border-none',
        '[&>hr]:bg-gradient-to-r [&>hr]:from-transparent [&>hr]:via-gray-300 [&>hr]:to-transparent [&>hr]:dark:via-gray-600',

        // 强调文本样式 - 优化深色主题下的可读性
        // 加粗文本：确保在深色主题下有足够对比度
        '[&_strong]:font-bold [&_strong]:text-gray-900 [&_strong]:dark:text-gray-50',
        '[&_b]:font-bold [&_b]:text-gray-900 [&_b]:dark:text-gray-50',

        // 斜体文本：保持良好的可读性
        '[&_em]:italic [&_em]:text-gray-800 [&_em]:dark:text-gray-100',
        '[&_i]:italic [&_i]:text-gray-800 [&_i]:dark:text-gray-100',

        // 组合强调（加粗+斜体）：最高对比度
        '[&_strong_em]:font-bold [&_strong_em]:italic [&_strong_em]:text-gray-900 [&_strong_em]:dark:text-gray-50',
        '[&_em_strong]:font-bold [&_em_strong]:italic [&_em_strong]:text-gray-900 [&_em_strong]:dark:text-gray-50',
        '[&_b_i]:font-bold [&_b_i]:italic [&_b_i]:text-gray-900 [&_b_i]:dark:text-gray-50',
        '[&_i_b]:font-bold [&_i_b]:italic [&_i_b]:text-gray-900 [&_i_b]:dark:text-gray-50',

        // 表格样式 - 优化的表格颜色
        '[&>table]:my-8 [&>table]:w-full [&>table]:border-collapse',
        '[&>table]:border [&>table]:border-gray-200 [&>table]:dark:border-gray-700',
        '[&>table]:overflow-hidden [&>table]:rounded-lg',

        '[&>table>thead]:bg-gray-50 [&>table>thead]:dark:bg-gray-800',
        '[&>table>thead>tr>th]:px-4 [&>table>thead>tr>th]:py-3 [&>table>thead>tr>th]:text-left',
        '[&>table>thead>tr>th]:font-semibold [&>table>thead>tr>th]:text-gray-900 [&>table>thead>tr>th]:dark:text-gray-100',

        '[&>table>tbody>tr]:border-t [&>table>tbody>tr]:border-gray-200 [&>table>tbody>tr]:dark:border-gray-700',
        '[&>table>tbody>tr>td]:px-4 [&>table>tbody>tr>td]:py-3 [&>table>tbody>tr>td]:text-gray-700 [&>table>tbody>tr>td]:dark:text-gray-300',

        className
      )}
      style={{
        // Medium标准的阅读体验
        fontSize: '1.25rem', // 20px - Medium标准字体大小
        lineHeight: '1.75', // 1.75倍行高 - Medium标准
        fontFamily: 'Georgia, serif', // Medium使用的字体
      }}
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}

/**
 * 检测内容格式
 */
export function detectContentFormat(
  content: string
): 'markdown' | 'html' | 'text' {
  // 检查是否包含HTML标签
  if (/<[^>]+>/.test(content)) {
    return 'html';
  }

  // 检查是否包含Markdown语法
  if (
    /^#{1,6}\s/.test(content) ||
    /\*\*.*\*\*/.test(content) ||
    /\[.*\]\(.*\)/.test(content)
  ) {
    return 'markdown';
  }

  return 'text';
}
