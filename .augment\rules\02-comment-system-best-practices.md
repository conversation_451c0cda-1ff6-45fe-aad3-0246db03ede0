---
description: 评论系统最佳实践规范 - SSR优先的评论架构设计
globs:
  - "src/components/blog/Comments*.tsx"
  - "src/components/blog/Comment*.tsx"
  - "src/app/**/blog/**/page.tsx"

alwaysApply: true
---

# 评论系统最佳实践规范

## 核心设计原则

### 1. SSR优先原则 (最高优先级) ⚠️

**🚫 评论内容必须SSR渲染**
- 评论文本内容必须在服务端渲染，确保搜索引擎可见
- 用户信息、时间戳、基础统计数据必须SSR
- 禁止整个评论组件使用'use client'指令

**✅ 正确的分层架构**
```tsx
// ✅ 正确：分层渲染
<CommentsSSR comments={ssrComments} />        // SSR内容展示
<CommentInteractions postId={postId} />       // CSR交互增强
<CommentForm postId={postId} />               // CSR表单处理

// ❌ 错误：整体客户端渲染
<Comments initialComments={comments} />       // 违反SSR优先原则
```

### 2. 渐进增强原则

**基础功能无JavaScript依赖**
- 评论内容在无JavaScript环境下完全可读
- 使用语义化HTML标签（article, header, time等）
- 添加结构化数据标记（Schema.org）

**交互功能客户端增强**
- 点赞、回复等交互功能通过JavaScript增强
- 使用骨架屏为交互元素占位
- 网络失败时优雅降级到本地存储

### 3. 性能优化原则

**首屏渲染优化**
- 评论内容SSR，避免"加载中"状态
- 交互功能延迟加载，不阻塞首屏渲染
- 避免不必要的客户端API请求

**状态管理优化**
- localStorage + API双重存储机制
- 乐观更新 + 错误回滚策略
- 智能状态同步（服务器状态优先）

## 推荐架构设计

### 组件分层架构

#### 1. CommentsSSR组件 - 纯SSR展示层
```tsx
// 职责：评论内容的服务端渲染
interface CommentsSSRProps {
  comments: Comment[];
  postId: string;
  className?: string;
}

// 特点：
// - 无'use client'指令
// - 语义化HTML结构
// - 结构化数据标记
// - 交互按钮骨架占位
```

#### 2. CommentInteractions组件 - 客户端增强层
```tsx
// 职责：为SSR内容添加交互功能
interface CommentInteractionsProps {
  postId: string;
  userId?: string;
  className?: string;
}

// 特点：
// - 'use client'指令
// - 延迟初始化
// - 状态持久化
// - 错误容错处理
```

#### 3. CommentForm组件 - 表单处理层
```tsx
// 职责：评论提交和表单管理
interface CommentFormProps {
  postId: string;
  userId?: string;
  allowGuests?: boolean;
  replyToCommentId?: string;
  onCommentAdded?: () => void;
}

// 特点：
// - 'use client'指令
// - 游客信息管理
// - 回复功能支持
// - 表单状态管理
```

### 数据流设计

#### SSR阶段数据流
```
博文页面 → getCommentsByPostId() → SSR评论数据 → CommentsSSR组件 → HTML输出
```

#### 客户端增强数据流
```
页面加载 → CommentInteractions初始化 → 增强SSR按钮 → 状态同步 → 交互就绪
```

#### 用户交互数据流
```
用户操作 → 乐观更新UI → API调用 → 成功：同步localStorage | 失败：回滚UI
```

## 技术实现规范

### 1. HTML结构规范

**语义化标签使用**
```html
<section itemScope itemType="https://schema.org/CommentAction">
  <article itemScope itemType="https://schema.org/Comment">
    <header>
      <span itemProp="author">用户名</span>
      <time itemProp="datePublished" dateTime="ISO8601">时间</time>
    </header>
    <div itemProp="text">评论内容</div>
  </article>
</section>
```

**交互按钮骨架**
```html
<!-- 为客户端增强预留的数据属性 -->
<button 
  class="comment-like-button"
  data-comment-id="comment-123"
  data-initial-count="5"
>
  <ThumbsUp /> 5
</button>
```

### 2. 状态管理规范

**localStorage结构**
```typescript
interface LikedCommentsStorage {
  [commentId: string]: {
    isLiked: boolean;
    timestamp: number;
  };
}

interface GuestInfoStorage {
  name: string;
  email: string;
}
```

**状态同步策略**
1. 页面加载：localStorage → UI状态
2. 用户操作：乐观更新 → API调用
3. API成功：服务器状态 → localStorage → UI状态
4. API失败：回滚UI状态，保持localStorage

### 3. 错误处理规范

**网络错误处理**
```typescript
try {
  // API调用
  const response = await fetch('/api/comments/like');
  // 成功处理
} catch (error) {
  // 回滚UI状态
  // 显示错误提示
  // 保持localStorage状态
}
```

**降级策略**
- API失败 → localStorage备份
- JavaScript禁用 → 纯HTML可读
- 网络断开 → 本地状态保持

## SEO优化规范

### 1. 结构化数据

**必需的Schema.org标记**
```html
<!-- 评论区域 -->
<section itemScope itemType="https://schema.org/CommentAction">

<!-- 单个评论 -->
<article itemScope itemType="https://schema.org/Comment">
  <span itemProp="author">作者</span>
  <time itemProp="datePublished">时间</time>
  <div itemProp="text">内容</div>
</article>
```

### 2. 内容可见性

**搜索引擎可见内容**
- ✅ 评论文本内容
- ✅ 用户名和时间戳
- ✅ 点赞数和回复数
- ✅ 评论层级结构

**搜索引擎不可见内容**
- ❌ 用户特定的点赞状态
- ❌ 实时交互功能
- ❌ 表单提交逻辑

### 3. 页面性能

**Core Web Vitals优化**
- LCP：评论内容SSR，首屏快速渲染
- FID：交互功能延迟加载，不阻塞主线程
- CLS：骨架屏占位，避免布局偏移

## 业界对比分析

### 主流平台实践

**Medium** - SSR + 客户端增强
- ✅ 评论内容SSR渲染
- ✅ 点赞状态客户端加载
- ✅ 骨架屏过渡效果

**Dev.to** - SSR + 客户端增强
- ✅ 评论内容SSR渲染
- ✅ localStorage + API双重存储
- ✅ 实时更新机制

**GitHub** - 混合渲染
- ✅ 评论内容SSR
- ✅ 交互状态API获取
- ✅ 渐进增强设计

### 技术方案评分

| 方案 | SEO | 性能 | 体验 | 维护 | 总分 |
|------|-----|------|------|------|------|
| 完全SSR | 10 | 8 | 4 | 6 | 7.0 |
| 完全CSR | 2 | 4 | 8 | 8 | 5.5 |
| **SSR+CSR混合** | **10** | **9** | **9** | **9** | **9.25** |
| 第三方服务 | 3 | 7 | 9 | 9 | 7.0 |

## 实施检查清单

### ✅ SSR实施检查
- [ ] 评论内容在HTML源码中可见
- [ ] 无'use client'指令在展示组件中
- [ ] 添加了结构化数据标记
- [ ] 使用了语义化HTML标签

### ✅ 性能优化检查
- [ ] 首屏无"加载中"状态
- [ ] 交互功能延迟加载
- [ ] 避免了重复API请求
- [ ] 实现了骨架屏占位

### ✅ 用户体验检查
- [ ] 点赞状态页面刷新后保持
- [ ] 网络失败时优雅降级
- [ ] 乐观更新即时反馈
- [ ] 错误提示用户友好

### ✅ 代码质量检查
- [ ] 组件职责单一清晰
- [ ] 状态管理逻辑完整
- [ ] 错误处理覆盖全面
- [ ] 类型定义准确完整

## 禁止的反模式

### ❌ 严重违规
```tsx
// ❌ 整个评论组件客户端渲染
'use client';
export function Comments() {
  // 违反SSR优先原则
}

// ❌ 评论内容依赖JavaScript
{isLoading ? <div>加载中...</div> : <CommentList />}
```

### ❌ 性能问题
```tsx
// ❌ 不必要的重复请求
useEffect(() => {
  loadComments(); // 即使有SSR数据也重新请求
}, []);

// ❌ 阻塞首屏渲染
const comments = await fetchComments(); // 同步等待
```

### ❌ SEO问题
```tsx
// ❌ 评论内容对搜索引擎不可见
<div style={{display: isLoaded ? 'block' : 'none'}}>
  {comments.map(comment => ...)}
</div>
```

记住：评论系统是用户生成内容(UGC)的重要来源，正确的SSR实现对SEO权重提升至关重要！
