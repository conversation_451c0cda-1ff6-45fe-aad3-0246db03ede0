create table public.comment_likes (
  id text not null,
  "commentId" text not null,
  "userId" text null,
  "ipAddress" text null,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  constraint comment_likes_pkey primary key (id),
  constraint comment_likes_commentId_fkey foreign key ("commentId") references comments (id) on update cascade on delete cascade,
  constraint comment_likes_userId_fkey foreign key ("userId") references users (id) on update cascade on delete cascade,
  constraint comment_likes_user_unique unique ("commentId", "userId"),
  constraint comment_likes_ip_unique unique ("commentId", "ipAddress"),
  constraint comment_likes_user_or_ip_check check (("userId" is not null) or ("ipAddress" is not null))
) tablespace pg_default;

-- 创建索引以提高查询性能
create index idx_comment_likes_comment_id on public.comment_likes using btree ("commentId");
create index idx_comment_likes_user_id on public.comment_likes using btree ("userId");
create index idx_comment_likes_ip_address on public.comment_likes using btree ("ipAddress");