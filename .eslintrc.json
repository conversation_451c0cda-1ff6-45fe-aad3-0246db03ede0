{
  "extends": ["next/core-web-vitals", "prettier"],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "project": "./tsconfig.json"
  },
  "plugins": ["@typescript-eslint", "prettier", "import"],
  "rules": {
    // TypeScript 规则
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_"
      }
    ],
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/no-var-requires": "error",

    // React 规则
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "react/display-name": "off",
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",

    // Next.js 规则
    "@next/next/no-img-element": "error",
    "@next/next/no-html-link-for-pages": "error",

    // 通用规则
    "no-console": "off",
    "no-debugger": "error",
    "no-alert": "off",
    "no-var": "error",
    "prefer-const": "error",
    "no-unused-expressions": "error",
    "no-duplicate-imports": "error",
    "no-multiple-empty-lines": [
      "error",
      {
        "max": 1,
        "maxEOF": 0
      }
    ],

    // Prettier 集成
    "prettier/prettier": [
      "error",
      {
        "endOfLine": "auto"
      }
    ],

    // 导入规则
    "import/order": [
      "error",
      {
        "groups": ["builtin", "external", "internal", "parent", "sibling", "index"],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ]
  },
  "settings": {
    "react": {
      "version": "detect"
    },
    "import/resolver": {
      "typescript": {
        "alwaysTryTypes": true,
        "project": "./tsconfig.json"
      }
    }
  },
  "env": {
    "browser": true,
    "es2022": true,
    "node": true
  },
  "ignorePatterns": ["node_modules/", ".next/", "out/", "dist/", "build/", "*.config.js", "*.config.ts"]
}
