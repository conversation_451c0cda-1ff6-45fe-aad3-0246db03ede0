'use client';

import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import React from 'react';

import { cn } from '@/lib/utils';

interface BlogPaginationProps {
  currentPage: number;
  totalPages: number;
  basePath: string;
  className?: string;
  showFirstLast?: boolean;
  maxVisiblePages?: number;
}

export function BlogPagination({
  currentPage,
  totalPages,
  basePath,
  className,
  showFirstLast = true,
  maxVisiblePages = 7,
}: BlogPaginationProps) {
  const t = useTranslations('common');

  if (totalPages <= 1) {
    return null;
  }

  // 计算显示的页码范围
  const getVisiblePages = () => {
    const pages: (number | 'ellipsis')[] = [];

    if (totalPages <= maxVisiblePages) {
      // 如果总页数不超过最大显示数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 复杂的分页逻辑
      const sidePages = Math.floor((maxVisiblePages - 3) / 2); // 减去首页、末页和当前页

      if (currentPage <= sidePages + 2) {
        // 当前页靠近开始
        for (let i = 1; i <= maxVisiblePages - 2; i++) {
          pages.push(i);
        }
        pages.push('ellipsis');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - sidePages - 1) {
        // 当前页靠近结束
        pages.push(1);
        pages.push('ellipsis');
        for (let i = totalPages - maxVisiblePages + 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 当前页在中间
        pages.push(1);
        pages.push('ellipsis');
        for (
          let i = currentPage - sidePages;
          i <= currentPage + sidePages;
          i++
        ) {
          pages.push(i);
        }
        pages.push('ellipsis');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const visiblePages = getVisiblePages();

  const buttonClasses = cn(
    'flex items-center justify-center w-10 h-10 text-sm font-medium',
    'border border-mystical-200 dark:border-dark-600 rounded-lg',
    'bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400',
    'hover:bg-mystical-50 dark:hover:bg-dark-700 hover:border-mystical-300 dark:hover:border-dark-500',
    'transition-all duration-200 ease-out',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-dark-800'
  );

  const activeButtonClasses = cn(
    buttonClasses,
    'bg-mystical-500 border-mystical-500 text-white',
    'hover:bg-mystical-600 hover:border-mystical-600'
  );

  const buildPageUrl = (page: number) => {
    if (page === 1) {
      return basePath;
    }
    return `${basePath}?page=${page}`;
  };

  return (
    <nav
      className={cn('flex items-center justify-center gap-2', className)}
      aria-label={t('pagination')}
    >
      {/* 首页按钮 */}
      {showFirstLast && currentPage > 1 && (
        <Link
          href={buildPageUrl(1)}
          className={buttonClasses}
          aria-label={t('firstPage')}
        >
          <span className='sr-only'>{t('firstPage')}</span>
          <svg className='h-4 w-4' fill='currentColor' viewBox='0 0 20 20'>
            <path
              fillRule='evenodd'
              d='M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z'
              clipRule='evenodd'
            />
          </svg>
        </Link>
      )}

      {/* 上一页按钮 */}
      {currentPage > 1 ? (
        <Link
          href={buildPageUrl(currentPage - 1)}
          className={buttonClasses}
          aria-label={t('previousPage')}
        >
          <ChevronLeft className='h-4 w-4' />
        </Link>
      ) : (
        <button
          className={buttonClasses}
          disabled
          aria-label={t('previousPage')}
        >
          <ChevronLeft className='h-4 w-4' />
        </button>
      )}

      {/* 页码按钮 */}
      {visiblePages.map((page, index) => {
        if (page === 'ellipsis') {
          return (
            <div
              key={`ellipsis-${index}`}
              className='flex h-10 w-10 items-center justify-center'
            >
              <MoreHorizontal className='h-4 w-4 text-mystical-400' />
            </div>
          );
        }

        const isActive = page === currentPage;

        return (
          <Link
            key={page}
            href={buildPageUrl(page)}
            className={isActive ? activeButtonClasses : buttonClasses}
            aria-label={`${t('page')} ${page}`}
            aria-current={isActive ? 'page' : undefined}
          >
            {page}
          </Link>
        );
      })}

      {/* 下一页按钮 */}
      {currentPage < totalPages ? (
        <Link
          href={buildPageUrl(currentPage + 1)}
          className={buttonClasses}
          aria-label={t('nextPage')}
        >
          <ChevronRight className='h-4 w-4' />
        </Link>
      ) : (
        <button className={buttonClasses} disabled aria-label={t('nextPage')}>
          <ChevronRight className='h-4 w-4' />
        </button>
      )}

      {/* 末页按钮 */}
      {showFirstLast && currentPage < totalPages && (
        <Link
          href={buildPageUrl(totalPages)}
          className={buttonClasses}
          aria-label={t('lastPage')}
        >
          <span className='sr-only'>{t('lastPage')}</span>
          <svg className='h-4 w-4' fill='currentColor' viewBox='0 0 20 20'>
            <path
              fillRule='evenodd'
              d='M4.293 4.293a1 1 0 011.414 0l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414zm6 0a1 1 0 011.414 0l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414-1.414L14.586 10l-4.293-4.293a1 1 0 010-1.414z'
              clipRule='evenodd'
            />
          </svg>
        </Link>
      )}
    </nav>
  );
}

// 简化版分页组件
export function SimplePagination({
  currentPage,
  totalPages,
  basePath,
  className,
}: {
  currentPage: number;
  totalPages: number;
  basePath: string;
  className?: string;
}) {
  const t = useTranslations('common');

  if (totalPages <= 1) {
    return null;
  }

  const buildPageUrl = (page: number) => {
    if (page === 1) {
      return basePath;
    }
    return `${basePath}?page=${page}`;
  };

  return (
    <div className={cn('flex items-center justify-between', className)}>
      {/* 上一页 */}
      <div>
        {currentPage > 1 ? (
          <Link
            href={buildPageUrl(currentPage - 1)}
            className='flex items-center gap-2 rounded-lg border border-mystical-200 bg-white px-4 py-2 text-sm font-medium text-mystical-600 transition-colors hover:bg-mystical-50 dark:border-dark-600 dark:bg-dark-800 dark:text-mystical-400 dark:hover:bg-dark-700'
          >
            <ChevronLeft className='h-4 w-4' />
            {t('previous')}
          </Link>
        ) : (
          <div className='flex cursor-not-allowed items-center gap-2 rounded-lg border border-mystical-200 bg-mystical-50 px-4 py-2 text-sm font-medium text-mystical-400 dark:border-dark-600 dark:bg-dark-700 dark:text-mystical-500'>
            <ChevronLeft className='h-4 w-4' />
            {t('previous')}
          </div>
        )}
      </div>

      {/* 页码信息 */}
      <div className='text-sm text-mystical-600 dark:text-mystical-400'>
        {t('pageInfo', { current: currentPage, total: totalPages })}
      </div>

      {/* 下一页 */}
      <div>
        {currentPage < totalPages ? (
          <Link
            href={buildPageUrl(currentPage + 1)}
            className='flex items-center gap-2 rounded-lg border border-mystical-200 bg-white px-4 py-2 text-sm font-medium text-mystical-600 transition-colors hover:bg-mystical-50 dark:border-dark-600 dark:bg-dark-800 dark:text-mystical-400 dark:hover:bg-dark-700'
          >
            {t('next')}
            <ChevronRight className='h-4 w-4' />
          </Link>
        ) : (
          <div className='flex cursor-not-allowed items-center gap-2 rounded-lg border border-mystical-200 bg-mystical-50 px-4 py-2 text-sm font-medium text-mystical-400 dark:border-dark-600 dark:bg-dark-700 dark:text-mystical-500'>
            {t('next')}
            <ChevronRight className='h-4 w-4' />
          </div>
        )}
      </div>
    </div>
  );
}
