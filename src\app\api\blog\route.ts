/**
 * 博客文章API路由 - GET /api/blog
 * 获取博客文章列表，支持分页、筛选、搜索
 */

import { NextRequest, NextResponse } from 'next/server';

import { DatabaseService } from '@/lib/database-service';
import type { BlogPost } from '@/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // 解析查询参数
    const locale = searchParams.get('locale') || undefined;
    const status = searchParams.get('status') || 'PUBLISHED';
    const category = searchParams.get('category') || undefined;
    const featured =
      searchParams.get('featured') === 'true'
        ? true
        : searchParams.get('featured') === 'false'
          ? false
          : undefined;
    const search = searchParams.get('search') || undefined;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const orderBy = searchParams.get('orderBy') || 'publishedAt';
    const orderDirection = (searchParams.get('orderDirection') || 'desc') as
      | 'asc'
      | 'desc';

    // 计算偏移量
    const offset = (page - 1) * limit;

    let posts: BlogPost[];
    let total: number;

    if (search) {
      // 搜索模式
      posts = await DatabaseService.searchBlogPosts(search, locale, limit);
      total = posts.length; // 简化处理，实际应该有单独的计数查询
    } else {
      // 常规列表模式
      posts = await DatabaseService.getBlogPosts({
        ...(locale && { locale }),
        ...(status && { status }),
        ...(category && { category }),
        ...(featured !== undefined && { featured }),
        limit,
        offset,
        orderBy,
        orderDirection,
      });

      // 获取总数（简化处理，实际应该优化）
      const allPosts = await DatabaseService.getBlogPosts({
        ...(locale && { locale }),
        ...(status && { status }),
        ...(category && { category }),
        ...(featured !== undefined && { featured }),
      });
      total = allPosts.length;
    }

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return NextResponse.json({
      success: true,
      data: posts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    });
  } catch (error: any) {
    console.error('Error fetching blog posts:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch blog posts',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // 验证必需字段
    const requiredFields = ['title', 'content', 'locale', 'category'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: `Missing required field: ${field}`,
          },
          { status: 400 }
        );
      }
    }

    // 生成slug（如果没有提供）
    if (!body.slug) {
      body.slug = generateSlug(body.title);
    }

    // 计算阅读时间（如果没有提供）
    if (!body.readingTime) {
      body.readingTime = calculateReadingTime(body.content);
    }

    // 生成摘要（如果没有提供）
    if (!body.excerpt) {
      body.excerpt = generateExcerpt(body.content);
    }

    // 设置默认值
    const postData = {
      title: body.title,
      slug: body.slug,
      content: body.content,
      excerpt: body.excerpt,
      coverImage: body.coverImage,
      locale: body.locale,
      category: body.category,
      tags: body.tags || [],
      status: body.status || 'DRAFT',
      publishedAt: body.status === 'PUBLISHED' ? new Date() : null,
      scheduledAt: body.scheduledAt ? new Date(body.scheduledAt) : null,
      readingTime: body.readingTime || 0,
      viewCount: 0,
      likeCount: 0,
      shareCount: 0,
      commentCount: 0,
      featured: body.featured || false,
      seoTitle: body.seoTitle,
      seoDescription: body.seoDescription,
      keywords: body.keywords || [],
      metadata: body.metadata,
    };

    const post = await DatabaseService.createBlogPost(postData);

    return NextResponse.json({
      success: true,
      data: post,
      message: 'Blog post created successfully',
    });
  } catch (error: any) {
    console.error('Error creating blog post:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create blog post',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// 辅助函数
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fa5]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 100); // 限制长度
}

function calculateReadingTime(content: string): number {
  // 移除HTML标签，计算纯文本字数
  const plainText = content.replace(/<[^>]*>/g, '');
  const wordsPerMinute = 200; // 平均阅读速度
  const wordCount = plainText.length;
  return Math.ceil(wordCount / wordsPerMinute);
}

function generateExcerpt(content: string, maxLength = 200): string {
  // 移除HTML标签，生成摘要
  const plainText = content.replace(/<[^>]*>/g, '');
  if (plainText.length <= maxLength) {
    return plainText;
  }
  return plainText.substring(0, maxLength).trim() + '...';
}
