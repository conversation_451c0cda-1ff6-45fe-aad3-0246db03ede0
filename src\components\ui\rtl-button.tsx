'use client';

import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import * as React from 'react';

import { useRTL, useRTLStyles } from '@/components/ui/rtl-provider';
import { cn } from '@/lib/utils';

const rtlButtonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive:
          'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline:
          'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary:
          'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        mystical:
          'bg-mystical-gradient text-white hover:opacity-90 shadow-mystical',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
      iconPosition: {
        left: '',
        right: '',
        none: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      iconPosition: 'none',
    },
  }
);

export interface RTLButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof rtlButtonVariants> {
  asChild?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right' | 'none';
  showDirectionalIcon?: boolean;
  directionalIconType?: 'chevron' | 'arrow';
}

const RTLButton = React.forwardRef<HTMLButtonElement, RTLButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      icon,
      iconPosition = 'none',
      showDirectionalIcon = false,
      directionalIconType = 'chevron',
      children,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button';
    const { isRTL } = useRTL();
    const rtlStyles = useRTLStyles();

    // 根据RTL方向调整图标位置
    const getAdjustedIconPosition = () => {
      if (iconPosition === 'none') return 'none';
      if (isRTL) {
        return iconPosition === 'left' ? 'right' : 'left';
      }
      return iconPosition;
    };

    const adjustedIconPosition = getAdjustedIconPosition();

    // 获取方向性图标
    const getDirectionalIcon = () => {
      if (!showDirectionalIcon) return null;

      if (directionalIconType === 'chevron') {
        return isRTL ? (
          <ChevronLeft className='h-4 w-4' />
        ) : (
          <ChevronRight className='h-4 w-4' />
        );
      }

      // 可以添加更多图标类型
      return null;
    };

    // 渲染图标
    const renderIcon = (position: 'left' | 'right') => {
      if (adjustedIconPosition !== position) return null;

      const iconElement = icon || getDirectionalIcon();
      if (!iconElement) return null;

      const iconClasses = cn(
        'flex-shrink-0',
        position === 'left'
          ? rtlStyles.marginEnd('2')
          : rtlStyles.marginStart('2')
      );

      return <span className={iconClasses}>{iconElement}</span>;
    };

    return (
      <Comp
        className={cn(
          rtlButtonVariants({ variant, size, iconPosition, className }),
          isRTL && 'rtl-button'
        )}
        ref={ref}
        {...props}
      >
        {renderIcon('left')}
        <span className='flex-1'>{children}</span>
        {renderIcon('right')}
      </Comp>
    );
  }
);
RTLButton.displayName = 'RTLButton';

export { RTLButton, rtlButtonVariants };

/**
 * RTL-aware navigation button component
 * RTL感知的导航按钮组件
 */
interface RTLNavButtonProps
  extends Omit<RTLButtonProps, 'showDirectionalIcon' | 'directionalIconType'> {
  direction: 'next' | 'previous';
  showIcon?: boolean;
}

export const RTLNavButton = React.forwardRef<
  HTMLButtonElement,
  RTLNavButtonProps
>(({ direction, showIcon = true, children, ...props }, ref) => {
  const { isRTL } = useRTL();

  // 根据方向和RTL状态确定图标位置
  const getIconPosition = (): 'left' | 'right' => {
    if (direction === 'next') {
      return isRTL ? 'left' : 'right';
    } else {
      return isRTL ? 'right' : 'left';
    }
  };

  // 获取合适的图标
  const getIcon = () => {
    if (!showIcon) return null;

    if (direction === 'next') {
      return isRTL ? (
        <ChevronLeft className='h-4 w-4' />
      ) : (
        <ChevronRight className='h-4 w-4' />
      );
    } else {
      return isRTL ? (
        <ChevronRight className='h-4 w-4' />
      ) : (
        <ChevronLeft className='h-4 w-4' />
      );
    }
  };

  return (
    <RTLButton
      ref={ref}
      icon={getIcon()}
      iconPosition={getIconPosition()}
      {...props}
    >
      {children}
    </RTLButton>
  );
});
RTLNavButton.displayName = 'RTLNavButton';

/**
 * RTL-aware icon button component
 * RTL感知的图标按钮组件
 */
interface RTLIconButtonProps
  extends Omit<RTLButtonProps, 'icon' | 'iconPosition'> {
  icon: React.ReactNode;
  flipInRTL?: boolean;
}

export const RTLIconButton = React.forwardRef<
  HTMLButtonElement,
  RTLIconButtonProps
>(({ icon, flipInRTL = false, className, ...props }, ref) => {
  const { isRTL } = useRTL();

  const iconElement =
    flipInRTL && isRTL ? <span className='icon-flip'>{icon}</span> : icon;

  return (
    <RTLButton ref={ref} size='icon' className={cn(className)} {...props}>
      {iconElement}
    </RTLButton>
  );
});
RTLIconButton.displayName = 'RTLIconButton';

/**
 * RTL-aware button group component
 * RTL感知的按钮组组件
 */
interface RTLButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

export function RTLButtonGroup({
  children,
  className,
  orientation = 'horizontal',
}: RTLButtonGroupProps) {
  const { isRTL } = useRTL();

  const groupClasses = cn(
    'inline-flex',
    orientation === 'horizontal'
      ? isRTL
        ? 'flex-row-reverse'
        : 'flex-row'
      : 'flex-col',
    className
  );

  return (
    <div className={groupClasses} role='group'>
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return child;

        // 为按钮组中的按钮添加特殊样式
        const isFirst = index === 0;
        const isLast = index === React.Children.count(children) - 1;

        let additionalClasses = '';

        if (orientation === 'horizontal') {
          if (isFirst && !isLast) {
            additionalClasses = isRTL ? 'rounded-l-none' : 'rounded-r-none';
          } else if (isLast && !isFirst) {
            additionalClasses = isRTL ? 'rounded-r-none' : 'rounded-l-none';
          } else if (!isFirst && !isLast) {
            additionalClasses = 'rounded-none';
          }
        } else {
          if (isFirst && !isLast) {
            additionalClasses = 'rounded-b-none';
          } else if (isLast && !isFirst) {
            additionalClasses = 'rounded-t-none';
          } else if (!isFirst && !isLast) {
            additionalClasses = 'rounded-none';
          }
        }

        return React.cloneElement(child, {
          className: cn(child.props.className, additionalClasses),
        });
      })}
    </div>
  );
}
