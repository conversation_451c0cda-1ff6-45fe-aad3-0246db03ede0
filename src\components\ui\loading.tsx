import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

// Spinner组件变体
const spinnerVariants = cva('animate-spin rounded-full border-2', {
  variants: {
    size: {
      xs: 'h-3 w-3 border',
      sm: 'h-4 w-4 border',
      md: 'h-6 w-6 border-2',
      lg: 'h-8 w-8 border-2',
      xl: 'h-12 w-12 border-2',
    },
    variant: {
      default: 'border-border border-t-foreground',
      mystical: 'border-mystical-200 border-t-mystical-600',
      golden: 'border-gold-200 border-t-gold-600',
      white: 'border-white/20 border-t-white',
    },
  },
  defaultVariants: {
    size: 'md',
    variant: 'default',
  },
});

// Progress组件变体
const progressVariants = cva(
  'w-full bg-background-secondary rounded-full overflow-hidden',
  {
    variants: {
      size: {
        sm: 'h-1',
        md: 'h-2',
        lg: 'h-3',
        xl: 'h-4',
      },
      variant: {
        default: '',
        mystical: 'bg-mystical-100 dark:bg-mystical-900/20',
        golden: 'bg-gold-100 dark:bg-gold-900/20',
      },
    },
    defaultVariants: {
      size: 'md',
      variant: 'default',
    },
  }
);

// Skeleton组件变体
const skeletonVariants = cva('animate-pulse bg-background-secondary rounded', {
  variants: {
    variant: {
      default: 'bg-background-secondary',
      mystical: 'bg-mystical-100 dark:bg-mystical-900/20',
      golden: 'bg-gold-100 dark:bg-gold-900/20',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

// Spinner组件接口
export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {
  label?: string;
}

// Progress组件接口
export interface ProgressProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof progressVariants> {
  value?: number;
  max?: number;
  label?: string;
  showValue?: boolean;
}

// Skeleton组件接口
export interface SkeletonProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof skeletonVariants> {
  width?: string | number;
  height?: string | number;
  circle?: boolean;
}

// Spinner组件
const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size, variant, label, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('inline-flex items-center gap-2', className)}
      {...props}
    >
      <div
        className={cn(spinnerVariants({ size, variant }))}
        role='status'
        aria-label={label || 'Loading'}
      >
        <span className='sr-only'>{label || 'Loading...'}</span>
      </div>
      {label && <span className='text-sm text-foreground-muted'>{label}</span>}
    </div>
  )
);
Spinner.displayName = 'Spinner';

// Progress组件
const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
  (
    {
      className,
      size,
      variant,
      value = 0,
      max = 100,
      label,
      showValue = false,
      ...props
    },
    ref
  ) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);

    const barVariants = {
      default: 'bg-mystical-600',
      mystical: 'bg-gradient-to-r from-mystical-500 to-mystical-600',
      golden: 'bg-gradient-to-r from-gold-500 to-gold-600',
    };

    return (
      <div ref={ref} className={cn('w-full', className)} {...props}>
        {(label || showValue) && (
          <div className='mb-2 flex items-center justify-between'>
            {label && (
              <span className='text-sm font-medium text-foreground'>
                {label}
              </span>
            )}
            {showValue && (
              <span className='text-sm text-foreground-muted'>
                {Math.round(percentage)}%
              </span>
            )}
          </div>
        )}
        <div className={cn(progressVariants({ size, variant }))}>
          <div
            className={cn(
              'h-full rounded-full transition-all duration-300 ease-out',
              barVariants[variant || 'default']
            )}
            style={{ width: `${percentage}%` }}
            role='progressbar'
            aria-valuenow={value}
            aria-valuemin={0}
            aria-valuemax={max}
          />
        </div>
      </div>
    );
  }
);
Progress.displayName = 'Progress';

// Skeleton组件
const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  (
    { className, variant, width, height, circle = false, style, ...props },
    ref
  ) => (
    <div
      ref={ref}
      className={cn(
        skeletonVariants({ variant }),
        circle && 'rounded-full',
        className
      )}
      style={{
        width,
        height,
        ...style,
      }}
      {...props}
    />
  )
);
Skeleton.displayName = 'Skeleton';

// LoadingDots组件 - 点状加载动画
const LoadingDots = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    variant?: 'default' | 'mystical' | 'golden';
    size?: 'sm' | 'md' | 'lg';
  }
>(({ className, variant = 'default', size = 'md', ...props }, ref) => {
  const sizeClasses = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3',
  };

  const variantClasses = {
    default: 'bg-foreground-muted',
    mystical: 'bg-mystical-500',
    golden: 'bg-gold-500',
  };

  return (
    <div
      ref={ref}
      className={cn('flex items-center space-x-1', className)}
      {...props}
    >
      {[0, 1, 2].map(i => (
        <div
          key={i}
          className={cn(
            'animate-pulse rounded-full',
            sizeClasses[size],
            variantClasses[variant]
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  );
});
LoadingDots.displayName = 'LoadingDots';

// LoadingOverlay组件 - 覆盖层加载
const LoadingOverlay = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    visible?: boolean;
    variant?: 'default' | 'mystical' | 'golden';
    blur?: boolean;
  }
>(
  (
    {
      className,
      visible = true,
      variant = 'default',
      blur = true,
      children,
      ...props
    },
    ref
  ) => {
    if (!visible) return null;

    return (
      <div
        ref={ref}
        className={cn(
          'absolute inset-0 z-50 flex items-center justify-center',
          blur ? 'backdrop-blur-sm' : '',
          'bg-background/80',
          className
        )}
        {...props}
      >
        <div className='flex flex-col items-center space-y-4'>
          <Spinner variant={variant} size='lg' />
          {children}
        </div>
      </div>
    );
  }
);
LoadingOverlay.displayName = 'LoadingOverlay';

// SkeletonText组件 - 文本骨架屏
const SkeletonText = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    lines?: number;
    variant?: 'default' | 'mystical' | 'golden';
  }
>(({ className, lines = 3, variant = 'default', ...props }, ref) => (
  <div ref={ref} className={cn('space-y-2', className)} {...props}>
    {Array.from({ length: lines }).map((_, i) => (
      <Skeleton
        key={i}
        variant={variant}
        height='1rem'
        width={i === lines - 1 ? '75%' : '100%'}
      />
    ))}
  </div>
));
SkeletonText.displayName = 'SkeletonText';

export {
  Spinner,
  Progress,
  Skeleton,
  LoadingDots,
  LoadingOverlay,
  SkeletonText,
  spinnerVariants,
  progressVariants,
  skeletonVariants,
};
