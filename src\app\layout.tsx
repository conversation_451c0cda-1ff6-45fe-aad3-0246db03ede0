import type { Metadata } from 'next';
import {
  Inter,
  Noto_Sans_SC,
  Noto_Sans_TC,
  Noto_Sans_JP,
  Noto_Sans,
} from 'next/font/google';

import '@/styles/globals.css';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { locales, languageConfig } from '@/i18n';

// 加载多语言字体
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const notoSansSC = Noto_Sans_SC({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  variable: '--font-noto-sans-sc',
  display: 'swap',
});

const notoSansTC = Noto_Sans_TC({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  variable: '--font-noto-sans-tc',
  display: 'swap',
});

const notoSansJP = Noto_Sans_JP({
  subsets: ['latin'],
  weight: ['400', '500', '700'],
  variable: '--font-noto-sans-jp',
  display: 'swap',
});

const notoSans = Noto_Sans({
  subsets: ['latin', 'devanagari'],
  weight: ['400', '500', '700'],
  variable: '--font-noto-sans',
  display: 'swap',
});

// 基础元数据
export const metadata: Metadata = {
  title: 'Mystical Website - Free Tarot, Astrology & Numerology Tests',
  description:
    'Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis. Accurate personality insights and life guidance.',
  keywords:
    'mystical tests, free tests, AI analysis, tarot, astrology, numerology',
  authors: [{ name: 'Mystical Website Team' }],
  creator: 'Mystical Website',
  publisher: 'Mystical Website',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com',
    siteName: 'Mystical Website',
    title: 'Mystical Website - Free Tarot, Astrology & Numerology Tests',
    description:
      'Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Mystical Website - Free Tarot, Astrology & Numerology Tests',
    description:
      'Professional online mystical platform offering free tarot, astrology, and numerology tests with AI-powered analysis.',
    creator: '@mystical_website',
  },
  verification: {
    google: process.env['NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION'] || null,
    other: {
      'msvalidate.01': process.env['NEXT_PUBLIC_BING_SITE_VERIFICATION'] || '',
    },
  },
  // 添加多语言替代链接
  alternates: {
    canonical:
      process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com',
    languages: Object.fromEntries(
      locales.map(locale => [
        languageConfig[locale].hreflang,
        `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/${locale}`,
      ])
    ),
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='en' suppressHydrationWarning>
      <head>
        {/* DNS预取 - 优化字体加载 */}
        <link rel='dns-prefetch' href='//fonts.googleapis.com' />
        <link rel='dns-prefetch' href='//fonts.gstatic.com' />
        <link rel='dns-prefetch' href='//api.mystical-website.com' />

        {/* 多语言hreflang标签 */}
        {locales.map(locale => (
          <link
            key={locale}
            rel='alternate'
            hrefLang={languageConfig[locale].hreflang}
            href={`${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/${locale}`}
          />
        ))}
        <link
          rel='alternate'
          hrefLang='x-default'
          href={
            process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'
          }
        />
      </head>
      <body
        className={` ${inter.variable} ${notoSansSC.variable} ${notoSansTC.variable} ${notoSansJP.variable} ${notoSans.variable} font-sans antialiased`}
        suppressHydrationWarning
      >
        <ThemeProvider
          attribute='class'
          defaultTheme='light'
          enableSystem={false}
          disableTransitionOnChange={false}
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
