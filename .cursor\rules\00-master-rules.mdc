description:
globs:

alwaysApply: true
---

# 玄学多语言网站开发 - 统一规范文档

## 项目概述

构建一个专业的多语言玄学网站，包含塔罗、星座、数字命理等东西方神秘学内容。采用内容驱动的SEO策略，通过高质量内容建立权威性，最终发展为可持续的外链服务业务。

## 核心开发原则

### 1. SEO至上原则

- **页面SEO配置**：每个页面必须包含完整的SEO元数据，使用Next.js的generateMetadata进行动态SEO
- **内容结构优化**：实现正确的标题层级结构 (h1 > h2 > h3)，包含结构化数据(JSON-LD)
- **性能指标优化**：优化核心网络指标(Core Web Vitals)，实现完整的站点地图和robots.txt
- **多语言SEO**：为每种语言提供独立的SEO元数据，正确配置hreflang标签

### 2. 性能优化原则

- **图片优化**：所有图片使用Next.js Image组件，配置适当的sizes属性，支持WebP和AVIF格式
- **代码分割**：使用动态导入(dynamic import)分割代码，预加载关键资源，延迟加载非关键内容
- **缓存策略**：实现多层缓存策略，包括浏览器缓存、CDN缓存和服务端缓存

### 3. 多语言架构原则

- **国际化框架**：使用next-intl进行国际化，支持多语言扩展策略
- **URL结构**：实现SEO友好的URL结构：/[locale]/[category]/[slug]
- **语言支持**：支持RTL（从右到左）语言的布局适配，针对不同语言的文字长度进行动态布局调整
- **文化适配**：实现文化敏感的颜色和符号系统，提供语言回退机制

### 4. 主题和视觉原则

- **默认浅色主题**：网站必须默认为白色背景，确保最佳的可读性和用户体验
- **深色模式支持**：提供深色主题切换功能，但不能作为默认主题
- **主题一致性**：确保所有组件在两种主题下都有良好的视觉效果
- **渐进增强**：浅色主题为基础，深色主题为增强功能

### 5. 移动端优先原则

- **响应式设计**：采用移动优先的响应式设计策略
- **触摸交互**：确保所有触摸目标不小于44px，实现流畅的触摸交互和手势支持
- **移动端优化**：优化移动端加载性能和用户体验，提供移动端专用的导航和布局组件
- **可访问性**：确保移动端的可访问性和易用性

### 6. 代码质量原则

- **类型安全**：严格的TypeScript配置，启用所有严格检查
- **错误处理**：实现完整的错误边界和错误处理
- **组件设计**：遵循组件单一职责原则，使用自定义Hooks抽象业务逻辑

## 技术栈架构

### 前端技术栈

- **Next.js 14 (App Router)** - SSR/SSG混合模式，SEO友好
- **TypeScript** - 严格类型检查，提升代码质量
- **Tailwind CSS** - 响应式设计，快速样式开发
- **Framer Motion** - 优雅的页面动画和过渡效果
- **React Hook Form** - 表单处理和验证
- **Zustand** - 轻量级状态管理
- **next-intl** - 国际化支持

### 后端和数据库

- **Next.js API Routes** - 服务端逻辑处理
- **Supabase PostgreSQL** - 主数据库，内置认证和实时功能
- **Prisma** - 数据库ORM，类型安全的数据库操作
- **数据库直接管理** - 简化的内容创建和编辑流程

### AI集成服务

- **通义千问 (Qwen)** - 阿里云大语言模型，主要AI服务提供商
- **豆包 (Doubao)** - 字节跳动AI模型，备用服务
- **智谱AI (ZhipuAI)** - 国产AI模型，多语言支持
- **提示词工程** - 专业的玄学领域提示词库

### 内容和媒体管理

- **数据库存储** - 统一的内容存储，支持富文本和多语言
- **Sharp** - 图片优化和处理
- **AI内容生成** - 直接生成并存储到数据库

### 部署和监控

- **Vercel** - 部署平台，边缘计算优化
- **Sentry** - 错误监控和性能分析

## 多语言扩展策略

### 第一阶段：核心市场语言 (立即实施)

- **英语 (en)** - 全球通用语，最大市场覆盖
- **简体中文 (zh-CN)** - 中国大陆市场，14亿人口
- **繁体中文 (zh-TW)** - 台湾、香港及海外华人市场
- **西班牙语 (es)** - 西班牙和拉美市场，5亿人口
- **葡萄牙语 (pt)** - 巴西和葡语区，2.6亿人口
- **印地语 (hi)** - 印度北部核心市场，6亿人口
- **日语 (ja)** - 日本高消费市场，1.25亿人口

### 第二阶段：重要区域语言 (6个月后)

- **德语 (de)** - 德国和德语区，1亿人口
- **法语 (fr)** - 法国和法语区，2.8亿人口
- **意大利语 (it)** - 意大利传统占星市场，6500万人口
- **俄语 (ru)** - 俄罗斯和东欧，2.6亿人口
- **韩语 (ko)** - 韩国新兴市场，5100万人口
- **阿拉伯语 (ar)** - 阿拉伯世界，4.2亿人口

## 项目结构设计

### 核心目录结构

```
mystical-website/
├── src/
│   ├── app/                             # Next.js App Router
│   │   ├── [locale]/                    # 多语言路由
│   │   │   ├── page.tsx                 # 首页（测试导航中心）
│   │   │   ├── blog/                    # 博客模块
│   │   │   ├── tarot/                   # 塔罗专题（内容+测试）
│   │   │   ├── astrology/               # 星座专题（内容+测试）
│   │   │   ├── numerology/              # 数字命理（内容+测试）
│   │   │   ├── crystal/                 # 水晶能量（内容+测试）
│   │   │   ├── palmistry/               # 手相学（内容+测试）
│   │   │   ├── dreams/                  # 梦境解析（内容+测试）
│   │   │   └── admin/                   # 管理后台
│   │   ├── api/                         # API路由
│   │   │   ├── tests/                   # 测试相关API
│   │   │   ├── ai/                      # AI服务API
│   │   │   └── blog/                    # 博客API
│   │   └── middleware.ts                # 中间件
│   ├── components/                      # React组件
│   │   ├── ui/                          # 基础UI组件
│   │   ├── layout/                      # 布局组件
│   │   ├── seo/                         # SEO组件
│   │   ├── blog/                        # 博客组件
│   │   ├── mystical/                    # 玄学专用组件
│   │   ├── tests/                       # 通用测试组件
│   │   └── ai/                          # AI集成组件
│   ├── lib/                             # 工具库和配置
│   ├── hooks/                           # 自定义Hooks
│   ├── stores/                          # 状态管理
│   ├── types/                           # TypeScript类型定义
│   └── styles/                          # 样式文件
├── messages/                            # 国际化翻译文件
├── prisma/                              # 数据库Schema和迁移
├── public/                              # 静态资源
└── docs/                                # 项目文档
```

### 架构设计说明

#### 1. 数据库存储策略

- **单一数据源**：所有内容存储在PostgreSQL数据库中，避免多重存储同步问题
- **AI友好**：AI生成的内容可以直接存储到数据库，无需文件操作
- **动态内容**：支持实时更新，无需重新构建
- **简化管理**：通过管理后台直接操作数据库，流程简单直接

#### 2. 内容+测试一体化设计

- **专题测试集成**：每个玄学专题都包含测试功能，如 `/tarot/test/`、`/astrology/test/` 等
- **SEO友好URL**：测试页面继承专题页面的SEO权重
- **用户体验一致性**：从内容到测试的无缝转换

#### 3. 页面策略设计

**主页设计**：

- **定位**：测试导航中心 + 品牌展示
- **核心元素**：英雄区块、特色测试网格、社会证明、内容预览
- **SEO目标**：免费玄学测试相关关键词

**专题页设计**：

- **定位**：专题介绍 + 测试入口 + 相关内容
- **核心元素**：专题介绍、测试CTA、入门指南、进阶内容
- **SEO目标**：专题相关长尾关键词

#### 4. URL结构规范

**多语言URL结构**：

- 首页：`/[locale]/`
- 博客：`/[locale]/blog/[category]/[slug]`
- 专题：`/[locale]/[category]/` (如 `/zh/tarot/`)
- 测试：`/[locale]/[category]/test/`
- 结果：`/[locale]/[category]/test/result/[id]`
- 分享：`/[locale]/[category]/test/share/[token]`

## 设计系统规范

### 颜色系统

- **主色调**：神秘紫色系 (mystical-50 到 mystical-900)
- **辅助色**：黄金色系 (gold-50 到 gold-900)
- **深色系**：神秘黑色系 (dark-50 到 dark-900)
- **星座色彩**：火象(红)、土象(绿)、风象(蓝)、水象(紫)

### 字体系统

- **主要字体**：Inter, Noto Sans (现代无衬线)
- **标题字体**：Playfair Display, Noto Serif (优雅衬线)
- **神秘字体**：Cinzel, Trajan Pro (装饰性)
- **多语言字体**：针对不同语言优化的字体栈

### 动画系统

- **神秘学主题动画**：mystical-glow, tarot-flip, crystal-shine, star-twinkle
- **通用动画**：fade-in, slide-up, scale-in, float
- **交互动画**：悬停效果、点击反馈、加载状态

### 组件设计原则

- **原子化设计**：Atoms → Molecules → Organisms → Templates
- **响应式优先**：移动端优先的设计策略
- **可访问性**：符合WCAG 2.1 AA标准
- **主题支持**：浅色/深色主题切换

## 组件架构规范

### 组件分层

- **基础组件 (Atoms)**：Button, Input, Icon, Badge, Typography
- **复合组件 (Molecules)**：SearchBox, Card, Modal, FormField
- **业务组件 (Organisms)**：Header, Footer, ProductGrid, BlogList
- **页面模板 (Templates)**：PageLayout, BlogTemplate, TestTemplate

### 状态管理

- **全局状态**：Zustand store (用户、UI、测试、博客状态)
- **组件状态**：React useState 和自定义 Hooks
- **服务端状态**：数据获取和缓存策略

### 性能优化

- **代码分割**：路由级别和组件级别的懒加载
- **图片优化**：Next.js Image组件，WebP/AVIF格式
- **缓存策略**：多层缓存，ISR静态生成

## 内容管理规范

### 博客系统

- **内容存储**：PostgreSQL数据库直接存储
- **AI内容生成**：直接生成并存储到数据库
- **SEO优化**：自动生成元数据、slug、阅读时间
- **多语言支持**：每种语言独立的内容和SEO

### 测试系统

- **测试类型**：塔罗、星座、数字命理、水晶、手相、梦境
- **AI分析**：多AI服务提供商，智能路由和故障转移
- **结果分享**：社交分享优化，病毒传播机制
- **用户体验**：流畅的测试流程，个性化结果展示

## 部署和监控规范

### 部署流程

- **自动化部署**：GitHub Actions + Vercel
- **环境管理**：开发、预览、生产环境配置

### 质量保证

- **代码质量**：TypeScript、ESLint、Prettier
- **测试策略**：单元测试、集成测试、E2E测试
- **安全措施**：环境变量管理、安全头配置
- **监控告警**：错误率、响应时间、可用性监控

## 用户系统规范

### 认证流程

- **注册登录**：邮箱注册、社交登录、邮箱验证
- **会话管理**：JWT token、刷新机制、安全存储
- **权限控制**：基于角色的权限系统
- **用户体验**：游客模式、渐进式注册引导

### 用户界面

- **认证表单**：登录、注册、密码重置表单
- **用户资料**：头像、个人信息、偏好设置

## 博文页面排版规范

### 字体系统设计

- **多语言字体栈**：针对中文、日文、阿拉伯文等不同语言优化字体选择
- **系统字体优先**：使用系统字体减少加载时间，提升性能
- **三套字体分工**：正文使用现代无衬线字体，标题使用优雅衬线字体，代码使用等宽字体
- **字重优化**：深色模式下适当减轻字重，避免过于刺眼

### 响应式字号系统

- **流体排版**：使用clamp()函数实现自适应字号，确保各种屏幕下的最佳效果
- **桌面端标准**：正文18-22px，标题36-56px，副标题20-24px
- **移动端优化**：正文16-18px，标题28-40px，确保触摸设备的可读性
- **超大屏适配**：1440px以上屏幕使用20px正文，提供更舒适的阅读体验
- **字间距调整**：标题使用负字间距(-0.02em)，正文使用微正字间距(0.01em)

### 行高和行长度规范

- **科学行长度**：桌面端65字符，移动端45字符，基于阅读研究的最佳实践
- **行高规则**：长文本1.6倍行高，移动端1.5倍，标题1.25倍，短文本组件1.3倍
- **视窗适配**：最大宽度限制为90%视窗宽度，确保各种设备的适配
- **阅读舒适度**：通过合理的行长和行高组合，减少眼部疲劳

### 颜色和主题系统

- **浅色主题**：白色背景配深色文字，确保最佳可读性和SEO友好
- **深色主题**：降低对比度设计，使用稍浅的背景色，护眼且现代
- **层次化颜色**：主要文字、次要文字、辅助文字的颜色层次分明
- **链接颜色**：浅色模式使用蓝色系，深色模式使用浅蓝色系
- **语义化颜色**：引用块、代码块、标注等特殊内容的颜色区分

#### 博文详情页优化颜色方案

**浅色主题文本颜色**：
- **主标题 (H1/H2)**：`text-gray-900` - 最深黑灰色，确保最佳可读性
- **次级标题 (H3/H4)**：`text-gray-800` - 深灰色，保持层次感
- **小标题 (H5/H6)**：`text-gray-700` - 中灰色
- **正文段落**：`text-gray-700` - 舒适的阅读灰色
- **首段特殊样式**：`text-gray-800` - 稍深突出重点
- **引用块**：`text-gray-600` - 较浅灰色区分引用内容
- **列表项**：`text-gray-700` - 与正文保持一致
- **链接**：`text-blue-600` - 标准蓝色链接
- **代码**：`text-gray-800` (内联), `text-gray-100` (代码块)

**深色主题文本颜色**：
- **主标题 (H1/H2)**：`text-gray-50` - 接近白色但不刺眼
- **次级标题 (H3/H4)**：`text-gray-100` - 浅灰色
- **小标题 (H5/H6)**：`text-gray-200` - 中浅灰色
- **正文段落**：`text-gray-300` - 最佳深色模式阅读颜色
- **首段特殊样式**：`text-gray-200` - 稍亮突出重点
- **引用块**：`text-gray-300` - 保持良好可读性
- **列表项**：`text-gray-300` - 与正文保持一致
- **链接**：`text-blue-400` - 深色模式蓝色链接
- **代码**：`text-gray-200` (内联), `text-gray-100` (代码块)

#### 博文页面头部区域颜色方案

**面包屑导航**：
- 浅色主题：`text-gray-500` 默认，`hover:text-gray-700` 悬停
- 深色主题：`text-gray-400` 默认，`hover:text-gray-300` 悬停

**文章标题**：
- 浅色主题：`text-gray-900` - 最深黑灰色确保标题突出
- 深色主题：`text-gray-50` - 接近白色但不刺眼

**文章摘要**：
- 浅色主题：`text-gray-600` - 中等灰色，与正文区分
- 深色主题：`text-gray-300` - 保持良好可读性

**作者信息和元数据**：
- 作者名称：`text-gray-900` (浅色), `text-gray-100` (深色)
- 发布信息：`text-gray-500` (浅色), `text-gray-400` (深色)
- 分类链接：`text-gray-600` (浅色), `text-gray-300` (深色)
- 边框：`border-gray-200` (浅色), `border-gray-700` (深色)

**互动按钮**：
- 默认状态：`text-gray-500` (浅色), `text-gray-400` (深色)
- 悬停效果：红心 `hover:text-red-500`，分享 `hover:text-blue-500`
- 背景悬停：`hover:bg-gray-100` (浅色), `hover:bg-gray-800` (深色)

#### 博文颜色方案设计原则

**可读性优先**：
- 使用经过验证的灰色系，符合网页阅读标准和人体工程学原理
- 所有颜色对比度符合WCAG 2.1 AA标准，确保可访问性
- 避免使用过于饱和的品牌色作为正文颜色，减少阅读疲劳

**视觉层次**：
- 通过不同深度的灰色建立清晰的信息层次
- 主标题使用最深色确保突出，次级内容使用相应浅色
- 保持链接的蓝色系，在可读性和网页标准间取得平衡

**主题一致性**：
- 深色模式下避免过强对比，使用稍浅的背景色保护视力
- 所有交互元素都有明确的悬停状态反馈
- 平滑的颜色过渡效果提升用户体验

### 间距和布局系统

- **垂直韵律**：统一的间距比例系统，从0.5rem到4rem的8级间距
- **元素间距**：文章标题、段落、列表、引用块的标准化间距
- **层次间距**：H2标题上方3rem，H3标题上方2rem，段落间1.5rem
- **移动端紧凑**：移动设备上适当减少间距，提高信息密度

### 特殊元素设计

- **引用块样式**：背景色、左边框、内边距、圆角的统一设计
  - 浅色主题：`bg-gray-50` 背景，`border-gray-300` 左边框，`text-gray-600` 文字
  - 深色主题：`bg-gray-800/50` 背景，`border-gray-600` 左边框，`text-gray-300` 文字
- **代码块优化**：语法高亮支持、横向滚动、合适的字号和行高
  - 代码块：`bg-gray-900` (浅色), `bg-gray-950` (深色)，`text-gray-100` 文字
  - 内联代码：`bg-gray-100` (浅色), `bg-gray-800` (深色)，相应的文字颜色
- **列表格式**：合理的缩进、项目间距、嵌套列表的视觉层次
  - 列表项：`text-gray-700` (浅色), `text-gray-300` (深色)
  - 标记符号：`text-gray-500` (浅色), `text-gray-400` (深色)
- **图片说明**：较小字号、次要颜色、合适的间距
- **表格样式**：
  - 边框：`border-gray-200` (浅色), `border-gray-700` (深色)
  - 表头：`bg-gray-50` (浅色), `bg-gray-800` (深色)
  - 表头文字：`text-gray-900` (浅色), `text-gray-100` (深色)
  - 表格内容：`text-gray-700` (浅色), `text-gray-300` (深色)

### 可访问性和性能

- **对比度标准**：符合WCAG 2.1 AA标准的颜色对比度
- **用户自定义**：支持用户浏览器字体大小设置
- **渐进增强**：基础样式确保所有设备可读，媒体查询提供优化体验
- **零布局偏移**：避免字体加载导致的布局跳动
- **高对比度模式**：为视觉障碍用户提供高对比度选项

### 多语言排版适配

- **RTL语言支持**：阿拉伯语、希伯来语的从右到左排版
- **CJK字体优化**：中日韩文字的字体选择和行高调整
- **字符密度适配**：不同语言的字符密度差异处理
- **文化敏感设计**：考虑不同文化的阅读习惯和审美偏好

## 数据库管理规范

### 数据库连接策略

- **避免直连**：不使用Prisma直连PostgreSQL，避免网络防火墙和连接池限制问题
- **API优先**：通过Supabase API进行所有数据库操作，确保连接稳定性
- **类型安全**：使用Prisma类型定义，但通过Supabase API执行操作
- **服务层模式**：创建统一的数据库服务层，封装所有数据库操作

### Schema管理流程

- **控制台管理**：通过Supabase控制台的SQL Editor管理表结构
- **手动同步**：手动更新prisma/schema.prisma文件保持类型定义同步
- **版本控制**：将schema变更记录在版本控制中
- **类型生成**：使用npx prisma generate生成类型定义

### 数据操作模式

- **统一服务层**：创建DatabaseService类封装所有数据库操作
- **错误处理**：统一的错误处理机制和异常管理
- **性能优化**：合理使用索引和查询优化
- **安全性**：使用Row Level Security (RLS)保护数据

### 监控和维护

- **性能监控**：通过Supabase控制台监控数据库性能
- **健康检查**：应用层实现数据库健康检查
- **连接状态**：监控连接池使用情况和查询性能
- **存储管理**：定期检查存储使用情况和清理策略

## 数据库表结构规范

### 核心表结构约定

#### 字段命名规范

- **主键**：统一使用 `id` (TEXT类型，cuid)
- **外键**：使用 `userId`、`postId` 等驼峰命名
- **时间戳**：使用 `createdAt`、`updatedAt` (带引号的驼峰命名)
- **计数字段**：使用 `Count` 后缀，如 `likeCount`、`viewCount`
- **布尔字段**：使用 `is` 前缀，如 `isApproved`、`isPublic`

#### 数据类型标准

- **ID字段**：`TEXT NOT NULL` (使用cuid)
- **计数器**：`INTEGER NOT NULL DEFAULT 0`
- **布尔值**：`BOOLEAN NOT NULL DEFAULT false`
- **时间戳**：`TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP`
- **短文本**：`VARCHAR(n)` 指定长度
- **长文本**：`TEXT`
- **JSON数据**：`JSONB`
- **数组**：`TEXT[]`

### 必需表结构定义

#### 1. 用户相关表

**users** - 用户基础信息
```sql
id: TEXT (主键)
email: TEXT NOT NULL UNIQUE
username: TEXT UNIQUE
avatar: TEXT
locale: TEXT NOT NULL DEFAULT 'en'
theme: TEXT NOT NULL DEFAULT 'light'
createdAt: TIMESTAMP(3) NOT NULL
updatedAt: TIMESTAMP(3) NOT NULL
```

**user_sessions** - 用户会话管理
```sql
id: TEXT (主键)
userId: TEXT NOT NULL (外键 -> users.id)
sessionToken: TEXT NOT NULL UNIQUE
refreshToken: TEXT NOT NULL UNIQUE
expiresAt: TIMESTAMP(3) NOT NULL
createdAt: TIMESTAMP(3) NOT NULL
updatedAt: TIMESTAMP(3) NOT NULL
```

#### 2. 博文相关表

**blog_posts** - 博文主表
```sql
id: TEXT (主键)
title: VARCHAR(200) NOT NULL
slug: VARCHAR(250) NOT NULL UNIQUE
content: TEXT NOT NULL
excerpt: VARCHAR(500)
coverImage: VARCHAR(500)
locale: VARCHAR(10) NOT NULL
category: VARCHAR(50) NOT NULL
tags: TEXT[]
status: PostStatus NOT NULL DEFAULT 'DRAFT'
publishedAt: TIMESTAMP(3)
scheduledAt: TIMESTAMP(3)
viewCount: INTEGER NOT NULL DEFAULT 0
likeCount: INTEGER NOT NULL DEFAULT 0
shareCount: INTEGER NOT NULL DEFAULT 0
commentCount: INTEGER NOT NULL DEFAULT 0
readingTime: INTEGER NOT NULL DEFAULT 0
featured: BOOLEAN NOT NULL DEFAULT false
seoTitle: VARCHAR(60)
seoDescription: VARCHAR(160)
keywords: TEXT[]
metadata: JSONB
createdAt: TIMESTAMP(3) NOT NULL
updatedAt: TIMESTAMP(3) NOT NULL
```

**blog_categories** - 博文分类
```sql
id: TEXT (主键)
name: VARCHAR(100) NOT NULL
slug: VARCHAR(120) NOT NULL UNIQUE
description: VARCHAR(500)
color: VARCHAR(7)
icon: VARCHAR(50)
image: VARCHAR(500)
locale: VARCHAR(10) NOT NULL
postCount: INTEGER NOT NULL DEFAULT 0
seoTitle: VARCHAR(60)
seoDescription: VARCHAR(160)
createdAt: TIMESTAMP(3) NOT NULL
updatedAt: TIMESTAMP(3) NOT NULL
```

#### 3. 交互功能表

**user_likes** - 用户点赞记录
```sql
id: TEXT (主键)
userId: TEXT (外键 -> users.id, 可空支持游客)
postId: TEXT NOT NULL (外键 -> blog_posts.id)
ipAddress: TEXT (游客点赞时记录IP)
createdAt: TIMESTAMP(3) NOT NULL
-- 唯一约束：(userId, postId) 和 (ipAddress, postId)
```

**user_favorites** - 用户收藏记录
```sql
id: TEXT (主键)
userId: TEXT NOT NULL (外键 -> users.id)
postId: TEXT NOT NULL (外键 -> blog_posts.id)
createdAt: TIMESTAMP(3) NOT NULL
-- 唯一约束：(userId, postId)
```

**share_records** - 分享记录
```sql
id: TEXT (主键)
postId: TEXT NOT NULL (外键 -> blog_posts.id)
userId: TEXT (外键 -> users.id, 可空)
platform: VARCHAR(50) NOT NULL
ipAddress: TEXT
userAgent: TEXT
createdAt: TIMESTAMP(3) NOT NULL
```

**blog_views** - 浏览记录
```sql
id: TEXT (主键)
postId: TEXT NOT NULL (外键 -> blog_posts.id)
userId: TEXT (外键 -> users.id, 可空)
ipAddress: TEXT
userAgent: TEXT
createdAt: TIMESTAMP(3) NOT NULL
```

**comments** - 评论系统
```sql
id: TEXT (主键)
content: TEXT NOT NULL
userId: TEXT (外键 -> users.id, 可空)
postId: TEXT NOT NULL (外键 -> blog_posts.id)
parentId: TEXT (外键 -> comments.id, 支持回复)
isApproved: BOOLEAN NOT NULL DEFAULT false
createdAt: TIMESTAMP(3) NOT NULL
updatedAt: TIMESTAMP(3) NOT NULL
```

### 必需索引规范

#### 性能优化索引
```sql
-- 博文表核心查询索引
blog_posts (locale, category)
blog_posts (status, publishedAt)
blog_posts (featured, status, publishedAt)
blog_posts (tags) USING GIN

-- 交互功能索引
user_likes (postId, userId)
user_likes (postId, ipAddress)
user_favorites (userId, postId)
share_records (postId)
blog_views (postId)
```

#### 唯一约束索引
```sql
-- 防重复操作
user_likes (userId, postId) UNIQUE
user_likes (ipAddress, postId) UNIQUE
user_favorites (userId, postId) UNIQUE
```

### 数据库函数规范

#### 统计计数器函数
```sql
-- 原子性更新计数器，避免并发问题
increment_view_count(post_id TEXT)
increment_like_count(post_id TEXT)
decrement_like_count(post_id TEXT)
increment_share_count(post_id TEXT)
increment_comment_count(post_id TEXT)
decrement_comment_count(post_id TEXT)
```

#### 查询辅助函数
```sql
-- 获取用户交互状态
get_user_interaction_status(post_id TEXT, user_id TEXT, ip_address TEXT)
-- 批量更新统计数据
update_post_stats(post_id TEXT)
```

### 数据完整性规则

#### 外键级联规则
- **用户删除**：user_likes, user_favorites 级联删除
- **博文删除**：所有相关记录级联删除
- **评论删除**：子评论的 parentId 设为 NULL

#### 数据验证规则
- **邮箱格式**：应用层验证邮箱格式
- **slug唯一性**：所有slug字段必须唯一
- **计数器非负**：使用 GREATEST(count - 1, 0) 防止负数
- **时间戳一致性**：updatedAt 必须 >= createdAt

### 开发操作规范

#### 禁止的操作
- ❌ 直接修改计数器字段 (使用专用函数)
- ❌ 硬删除用户数据 (使用软删除或匿名化)
- ❌ 跳过外键约束检查
- ❌ 在生产环境直接执行DDL

#### 推荐的操作
- ✅ 使用 DatabaseService 类进行所有数据库操作
- ✅ 通过 Supabase API 执行查询
- ✅ 使用事务处理复杂操作
- ✅ 记录所有schema变更

记住：这个项目的核心目标是通过高质量内容建立SEO权威性，最终发展为可持续的外链服务业务。简化的技术架构让我们能专注于内容质量而不是复杂的技术实现。