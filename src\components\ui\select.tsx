import { cva, type VariantProps } from 'class-variance-authority';
import { ChevronDown } from 'lucide-react';
import * as React from 'react';

import { cn } from '@/lib/utils';

// Select组件变体
const selectVariants = cva(
  'flex h-10 w-full appearance-none rounded-lg border bg-background px-3 py-2 text-sm transition-all duration-200 placeholder:text-foreground-muted focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'border-border focus-visible:ring-2 focus-visible:ring-mystical-500 focus-visible:ring-offset-2',
        mystical:
          'border-mystical-300 focus-visible:border-mystical-500 focus-visible:ring-2 focus-visible:ring-mystical-500 focus-visible:ring-offset-2 focus-visible:shadow-mystical',
        golden:
          'border-gold-300 focus-visible:border-gold-500 focus-visible:ring-2 focus-visible:ring-gold-500 focus-visible:ring-offset-2 focus-visible:shadow-gold',
        ghost:
          'border-transparent bg-background-secondary focus-visible:border-mystical-300 focus-visible:bg-background',
      },
      size: {
        sm: 'h-8 px-2 text-xs',
        default: 'h-10 px-3',
        lg: 'h-12 px-4 text-base',
      },
      state: {
        default: '',
        error:
          'border-red-500 focus-visible:ring-red-500 text-red-900 placeholder:text-red-400',
        success:
          'border-green-500 focus-visible:ring-green-500 text-green-900 placeholder:text-green-400',
        warning:
          'border-yellow-500 focus-visible:ring-yellow-500 text-yellow-900 placeholder:text-yellow-400',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      state: 'default',
    },
  }
);

// Select组件接口
export interface SelectProps
  extends React.SelectHTMLAttributes<HTMLSelectElement>,
    VariantProps<typeof selectVariants> {
  error?: string;
  helperText?: string;
  label?: string;
  options?: Array<{
    value: string;
    label: string;
    disabled?: boolean;
  }>;
  placeholder?: string;
}

// Select组件
const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      className,
      variant,
      size,
      state,
      error,
      helperText,
      label,
      options = [],
      placeholder,
      id,
      children,
      ...props
    },
    ref
  ) => {
    const selectId = id || React.useId();
    const hasError = error || state === 'error';
    const actualState = hasError ? 'error' : state;

    return (
      <div className='w-full'>
        {label && (
          <label
            htmlFor={selectId}
            className='mb-2 block text-sm font-medium text-foreground'
          >
            {label}
          </label>
        )}
        <div className='relative'>
          <select
            id={selectId}
            className={cn(
              selectVariants({ variant, size, state: actualState }),
              'pr-10',
              className
            )}
            ref={ref}
            {...props}
          >
            {placeholder && (
              <option value='' disabled>
                {placeholder}
              </option>
            )}
            {options.map(option => (
              <option
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
            {children}
          </select>
          <ChevronDown className='pointer-events-none absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 opacity-50' />
        </div>
        {(error || helperText) && (
          <p
            className={cn(
              'mt-2 text-xs',
              hasError ? 'text-red-600' : 'text-foreground-muted'
            )}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);
Select.displayName = 'Select';

// SelectGroup组件
const SelectGroup = React.forwardRef<
  HTMLOptGroupElement,
  React.OptgroupHTMLAttributes<HTMLOptGroupElement>
>(({ className, ...props }, ref) => (
  <optgroup ref={ref} className={cn('font-medium', className)} {...props} />
));
SelectGroup.displayName = 'SelectGroup';

// SelectOption组件
const SelectOption = React.forwardRef<
  HTMLOptionElement,
  React.OptionHTMLAttributes<HTMLOptionElement>
>(({ className, ...props }, ref) => (
  <option ref={ref} className={cn(className)} {...props} />
));
SelectOption.displayName = 'SelectOption';

// Checkbox组件
const Checkbox = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    label?: string;
    error?: string;
    helperText?: string;
  }
>(({ className, label, error, helperText, id, ...props }, ref) => {
  const checkboxId = id || React.useId();

  return (
    <div className='flex flex-col space-y-2'>
      <div className='flex items-center space-x-2'>
        <input
          id={checkboxId}
          type='checkbox'
          className={cn(
            'h-4 w-4 rounded border-border text-mystical-600 focus:ring-2 focus:ring-mystical-500 focus:ring-offset-2',
            error && 'border-red-500',
            className
          )}
          ref={ref}
          {...props}
        />
        {label && (
          <label htmlFor={checkboxId} className='text-sm text-foreground'>
            {label}
          </label>
        )}
      </div>
      {(error || helperText) && (
        <p
          className={cn(
            'text-xs',
            error ? 'text-red-600' : 'text-foreground-muted'
          )}
        >
          {error || helperText}
        </p>
      )}
    </div>
  );
});
Checkbox.displayName = 'Checkbox';

// Radio组件
const Radio = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    label?: string;
    error?: string;
    helperText?: string;
  }
>(({ className, label, error, helperText, id, ...props }, ref) => {
  const radioId = id || React.useId();

  return (
    <div className='flex flex-col space-y-2'>
      <div className='flex items-center space-x-2'>
        <input
          id={radioId}
          type='radio'
          className={cn(
            'h-4 w-4 border-border text-mystical-600 focus:ring-2 focus:ring-mystical-500 focus:ring-offset-2',
            error && 'border-red-500',
            className
          )}
          ref={ref}
          {...props}
        />
        {label && (
          <label htmlFor={radioId} className='text-sm text-foreground'>
            {label}
          </label>
        )}
      </div>
      {(error || helperText) && (
        <p
          className={cn(
            'text-xs',
            error ? 'text-red-600' : 'text-foreground-muted'
          )}
        >
          {error || helperText}
        </p>
      )}
    </div>
  );
});
Radio.displayName = 'Radio';

export { Select, SelectGroup, SelectOption, Checkbox, Radio, selectVariants };
